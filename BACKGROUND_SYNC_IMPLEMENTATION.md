# Background Sync Implementation

This document describes the implementation of background API calls that can resume after app restarts.

## Overview

The implementation provides a robust background sync system that:

1. Calls `/data/company-categories` and `/company/get-all` APIs in the background
2. Persists sync state to resume after app restarts
3. Handles network connectivity changes
4. Provides retry logic with exponential backoff
5. Shows sync progress to users

## Architecture

### Core Components

1. **BackgroundSyncManager** (`src/services/backgroundSyncManager.ts`)

   - Main orchestrator for background sync operations
   - Handles app state changes and network connectivity
   - Manages sync tasks and retry logic

2. **SyncState Model** (`src/database/watermelon/models/SyncState.ts`)

   - WatermelonDB model to track sync progress
   - Stores sync status, progress, errors, and retry counts

3. **SyncState Repository** (`src/database/watermelon/repositories/syncStateRepository.ts`)

   - Database operations for sync state management
   - CRUD operations and query methods

4. **Company API Service** (`src/services/companyApiService.ts`)

   - Handles all company-related API calls
   - Includes `/company/get-all` endpoint

5. **Background Sync Hook** (`src/hooks/useBackgroundSync.ts`)

   - React hook to monitor sync status in components
   - Provides real-time sync progress updates

6. **Sync Status Indicator** (`src/components/SyncStatusIndicator.tsx`)
   - UI component to show sync progress
   - Only visible when sync is active (configurable)

### Database Schema Changes

Added new `sync_state` table with schema version 2:

- `sync_key`: Unique identifier for sync task ('categories' | 'companies')
- `status`: Current status ('pending' | 'in_progress' | 'completed' | 'failed')
- `progress`: Progress percentage (0-100)
- `last_sync_at`: Timestamp of last successful sync
- `error_message`: Error details if sync failed
- `retry_count`: Number of retry attempts

## API Endpoints

### 1. Categories Sync

- **Endpoint**: `/category` (existing)
- **Method**: GET
- **Purpose**: Sync category data
- **Handled by**: `categoryApiService.fetchCategories()`

### 2. Companies Sync

- **Endpoint**: `/company/get-all?page={page}&limit={limit}&sortBy=created_at&sortOrder=DESC&search=`
- **Method**: GET
- **Purpose**: Sync all company data (~13k records)
- **Pagination**: Automatic pagination handling (100 records per page)
- **Handled by**: `companyApiService.fetchAllCompanies()` with `fetchCompaniesPage()`

## Sync Flow

### Initial App Launch

1. `BackgroundSyncManager.initialize()` is called in `App.tsx`
2. Network and app state listeners are set up
3. If network is available, sync tasks are initiated
4. Sync state is persisted to database

### Background Sync Process

1. Check if sync is already completed or in progress
2. Update sync state to 'in_progress'
3. Execute API call and data processing
4. Update progress incrementally
5. Mark as 'completed' or 'failed' with error details
6. Implement retry logic for failed syncs

### App Restart Resume

1. On app launch, check for pending/failed syncs
2. Resume incomplete syncs if network is available
3. Continue from where sync left off

### Network Reconnection

1. Network state listener detects connectivity
2. Automatically resume pending syncs
3. No user intervention required

### Pagination Handling (Companies)

1. Fetch companies in pages of 500 records each
2. Extract pagination info from API response
3. Continue fetching until all pages are processed
4. Update progress incrementally during pagination
5. Handle ~13k records across ~27 pages efficiently (500 records per page)
6. Safety limit of 1000 pages to prevent infinite loops

## Usage

### Monitoring Sync Status

```typescript
import {useBackgroundSync, useAllSyncStatus} from '../hooks/useBackgroundSync';

// Monitor specific sync
const categoriesSync = useBackgroundSync('categories');
console.log(categoriesSync.status, categoriesSync.progress);

// Monitor all syncs
const {isAnySyncInProgress, allSyncsCompleted} = useAllSyncStatus();
```

### Showing Sync Progress

```typescript
import SyncStatusIndicator from '../components/SyncStatusIndicator';

// Show only when sync is active
<SyncStatusIndicator showOnlyWhenActive={true} />

// Always show sync status
<SyncStatusIndicator showOnlyWhenActive={false} />
```

### Manual Sync Reset

```typescript
import backgroundSyncManager from '../services/backgroundSyncManager';

// Reset specific sync
await backgroundSyncManager.resetSync('companies');

// Check sync status
const status = await backgroundSyncManager.getSyncStatus('categories');
```

## Error Handling

### Retry Logic

- Maximum 3 retry attempts per sync task
- Exponential backoff: 5s, 10s, 15s delays
- Failed syncs marked as 'failed' after max retries
- Automatic retry on network reconnection

### Error Persistence

- Error messages stored in sync state
- Detailed logging for debugging
- Graceful fallback to local data

## Performance Considerations

### Batch Operations

- Companies synced in chunks of 100 records
- Uses WatermelonDB batch operations for efficiency
- Progress updates during chunked processing

### Memory Management

- Large datasets processed incrementally
- Database operations use transactions
- Cleanup of old sync states

## Integration Points

### App.tsx

- Initializes background sync manager
- Sets up cleanup on app unmount

### CategoriesScreen.tsx

- Shows sync status indicator
- Continues to work with existing data flow

### Database Migration

- Automatic schema migration from v1 to v2
- Preserves existing data during upgrade

## Configuration

### Sync Keys

- `'categories'`: Category data sync
- `'companies'`: Company data sync

### Timeouts

- API timeout: 60 seconds (for large datasets)
- Retry delays: 5s, 10s, 15s (exponential backoff)

### Progress Updates

- Categories: 0% → 50% → 100%
- Companies: 0% → 10% (clear) → 80% (fetch pages) → 100% (save to DB)
  - Detailed pagination progress tracking (~27 pages for 13k records)
  - Real-time updates during page fetching and database saving
  - **80% fewer API calls** with 500 records per page

## Testing

### Manual Testing

1. Launch app with network connection
2. Observe sync progress in Categories screen
3. Force close app during sync
4. Relaunch app and verify sync resumes
5. Test with network disconnection/reconnection

### Debug Information

- Development builds show detailed sync status
- Console logs for all sync operations
- Error details in sync state records

## Future Enhancements

1. **Incremental Sync**: Only sync changed records
2. **Sync Scheduling**: Periodic background sync
3. **Conflict Resolution**: Handle data conflicts
4. **Sync Analytics**: Track sync performance metrics
5. **User Controls**: Manual sync triggers and settings
