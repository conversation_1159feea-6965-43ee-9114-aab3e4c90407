# Android App Shortcuts Troubleshooting Guide

## 🔍 Issue: Shortcuts Not Appearing on Home Screen

You're getting a success message but shortcuts aren't appearing. This is a common issue with several possible causes.

## 🛠️ Debugging Steps

### Step 1: Use the Debug Panel
1. **Install the updated APK** with the debug panel
2. **Open the app** and go to Categories screen
3. **Tap "🔧 Shortcut Debug"** button (green button)
4. **Check the debug information**:
   - Is "Shortcuts Supported" showing `true`?
   - What Android version are you testing on?
   - Are there any error messages?

### Step 2: Test Different Methods
In the debug panel, try:
1. **"Test Shortcut Creation"** - Tests the modern method
2. **"Test Legacy Shortcut"** - Tests the older method
3. **Check "Existing Shortcuts"** - See if shortcuts are created but not pinned

### Step 3: Manual Testing
Try creating a shortcut manually:
```bash
# Connect your device and run:
adb shell am start -W -a android.intent.action.VIEW -d "indiacustomercare://categories" com.indiacustomercare
```

## 🔧 Common Issues & Solutions

### Issue 1: User Didn't Confirm Dialog
**Problem**: On Android 8.0+, users must confirm shortcut creation
**Solution**: 
- Look for a system dialog asking to "Add to Home screen"
- Make sure to tap "Add" or "Allow"
- Some launchers show different dialogs

### Issue 2: Launcher Compatibility
**Problem**: Some launchers don't support pinned shortcuts
**Solution**:
- Test on stock Android launcher
- Try different launcher apps (Nova, Action Launcher, etc.)
- Check launcher settings for shortcut permissions

### Issue 3: Android Version Issues
**Problem**: Different Android versions handle shortcuts differently
**Solutions**:
- **Android 8.0+ (API 26+)**: Uses modern ShortcutManager
- **Android 7.1 (API 25)**: Limited shortcut support
- **Android 7.0 and below**: Uses legacy broadcast method

### Issue 4: App Permissions
**Problem**: App might not have permission to create shortcuts
**Solution**:
1. Go to **Settings > Apps > [Your App] > Permissions**
2. Look for "Create shortcuts" or similar permission
3. Enable if disabled

### Issue 5: Intent Configuration
**Problem**: Shortcut intent might be malformed
**Solution**: The updated code now uses:
- Direct activity intent instead of URL scheme
- Proper intent flags
- Deep link data in extras

## 📱 Device-Specific Testing

### Samsung Devices
- May require "Add icon to Home screen" permission
- Check Samsung launcher settings
- Try with One UI home launcher

### Xiaomi/MIUI
- Check "Display pop-up windows while running in background"
- Enable "Auto-start" permission
- Test with MIUI launcher vs. other launchers

### OnePlus/OxygenOS
- Check launcher settings for shortcut permissions
- Try with OnePlus launcher vs. stock Android

### Huawei/EMUI
- Check "Display on lock screen" permission
- Enable "Auto-launch" if available

## 🧪 Testing Commands

### Check if shortcuts are created:
```bash
adb shell dumpsys shortcut
```

### Test deep link manually:
```bash
adb shell am start -W -a android.intent.action.VIEW -d "indiacustomercare://categories" com.indiacustomercare
```

### View app logs:
```bash
adb logcat | grep -E "(AppShortcut|MainActivity|indiacustomercare)"
```

### Check app permissions:
```bash
adb shell dumpsys package com.indiacustomercare | grep permission
```

## 🔄 Alternative Solutions

### Solution 1: Force Legacy Method
If modern shortcuts don't work, modify the code to always use legacy:

```kotlin
// In AppShortcutModule.kt, change createShortcut method:
@ReactMethod
fun createShortcut(shortcutId: String, label: String, deepLink: String, promise: Promise) {
    try {
        // Always use legacy method for testing
        createLegacyShortcut(label, deepLink, promise)
    } catch (e: Exception) {
        promise.reject("SHORTCUT_ERROR", "Failed to create shortcut: ${e.message}", e)
    }
}
```

### Solution 2: Add Shortcut Permission Request
Add explicit permission request in AndroidManifest.xml:

```xml
<uses-permission android:name="android.permission.INSTALL_SHORTCUT" />
<uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" />
```

### Solution 3: Alternative Shortcut Method
Try using a different approach with ACTION_CREATE_SHORTCUT:

```kotlin
val intent = Intent("com.android.launcher.action.INSTALL_SHORTCUT").apply {
    putExtra("duplicate", false)
    putExtra(Intent.EXTRA_SHORTCUT_NAME, label)
    putExtra(Intent.EXTRA_SHORTCUT_ICON_RESOURCE, 
        Intent.ShortcutIconResource.fromContext(activity, R.mipmap.ic_launcher))
    putExtra(Intent.EXTRA_SHORTCUT_INTENT, shortcutIntent)
}
activity.sendBroadcast(intent)
```

## 📋 Checklist for Testing

- [ ] App builds successfully
- [ ] Debug panel shows "Shortcuts Supported: true"
- [ ] No error messages in debug panel
- [ ] Test shortcut creation shows success message
- [ ] Check home screen immediately after creation
- [ ] Try different launcher apps
- [ ] Test on different Android versions
- [ ] Check app permissions in device settings
- [ ] Look for system confirmation dialogs
- [ ] Test deep links work manually

## 🎯 Expected Behavior

### Successful Shortcut Creation:
1. User taps "Add to Home Screen"
2. System may show confirmation dialog
3. User confirms (if prompted)
4. Shortcut appears on home screen with app icon
5. Tapping shortcut opens app to specific screen

### If Still Not Working:
1. **Check device compatibility** - Some devices/launchers don't support shortcuts
2. **Try different test devices** - Test on multiple Android devices
3. **Use alternative methods** - Consider using different shortcut creation approaches
4. **Check system logs** - Look for error messages in device logs

## 📞 Next Steps

1. **Install the updated APK** with debug panel
2. **Use the debug panel** to identify the specific issue
3. **Test on multiple devices** if possible
4. **Report findings** - Share what the debug panel shows

The debug panel will help identify exactly what's happening with shortcut creation on your specific device!
