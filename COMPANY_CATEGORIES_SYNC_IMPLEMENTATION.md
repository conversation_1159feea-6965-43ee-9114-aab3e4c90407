# Company Categories Background Sync Implementation

## Overview

This implementation adds background sync functionality for the company-categories API endpoint, which manages the many-to-many relationship between companies and categories. The system follows the same 1-week interval pattern as the company sync to avoid unnecessary API calls.

## API Details

**Endpoint**: `https://india-customer-care-api.apps.openxcell.dev/app/v1/data/company-categories`

**Response Format**:
```json
{
  "message": "Company-category relationships retrieved successfully",
  "data": [
    {
      "id": 1,
      "companyId": 1,
      "categoryId": 1
    },
    {
      "id": 2,
      "companyId": 2,
      "categoryId": 2
    }
  ]
}
```

## Database Schema

### New Table: `company_categories`

| Column | Type | Description |
|--------|------|-------------|
| id | INTEGER | Primary key, auto-incremented (WatermelonDB) |
| api_id | INTEGER | Original API id field |
| company_id | INTEGER | API companyId (references companies) |
| category_id | INTEGER | API categoryId (references categories) |
| created_at | INTEGER | Timestamp |
| updated_at | INTEGER | Timestamp |

## Implementation Details

### 1. Database Layer

#### Model: `CompanyCategory.ts`
- WatermelonDB model for the company_categories table
- Fields: apiId, companyId, categoryId, createdAt, updatedAt

#### Repository: `companyCategoryRepository.ts`
- Full CRUD operations
- Batch operations for efficient data handling
- Query methods by company ID and category ID
- Count method for sync decision logic
- `batchCreateOrUpdate()` method for handling duplicates

### 2. API Service Layer

#### Service: `companyCategoryApiService.ts`
- Fetches all company-category relationships
- Helper methods for filtering by company ID or category ID
- Comprehensive error handling and logging

### 3. Background Sync Integration

#### Updated `BackgroundSyncManager`
- Added `company_categories` to SyncKey type
- New sync task: `syncCompanyCategories()`
- Weekly interval logic: `shouldSyncCompanyCategories()`
- Force sync method: `forceSyncCompanyCategories()`
- Last sync date getter: `getLastCompanyCategorySyncDate()`

#### Sync Logic Flow:
1. **Check Local Data**: Count existing company categories
2. **Check Last Sync**: Verify if a week has passed since last sync
3. **Sync Decision**: 
   - Sync if no local data OR more than 7 days since last sync
   - Skip if data exists AND less than 7 days since last sync
4. **Execute Sync**: Clear existing data, fetch from API, store locally

### 4. Hook Updates

#### Updated `useBackgroundSync.ts`
- Added support for `company_categories` sync key
- Updated `useAllSyncStatus()` to include company categories
- Progress calculation now includes all three sync tasks

## Sync Process Details

### Company Categories Sync Steps:

1. **Clear Existing Data (10% progress)**
   - Remove all existing company-category relationships
   - Ensures clean state for new data

2. **Fetch from API (10% - 80% progress)**
   - Call `/data/company-categories` endpoint
   - Process response data
   - Log sample data for debugging

3. **Store Locally (80% - 100% progress)**
   - Convert API format to local format
   - Use `batchCreateOrUpdate()` for efficient storage
   - Handle duplicates based on API id

### Weekly Interval Logic:

```typescript
// Check if sync is needed
const shouldSync = await shouldSyncCompanyCategories();

// Decision matrix:
// Local Count | Last Sync Date | Action
// 0           | Any           | Sync (no data)
// >0          | None          | Sync (no previous sync)
// >0          | <7 days ago   | Skip (data is fresh)
// >0          | ≥7 days ago   | Sync (data is stale)
```

## Testing

### Test Utility Updates

Enhanced `testWeeklySync.ts` with company categories methods:

```typescript
// Force sync company categories
await WeeklySyncTester.forceSyncCompanyCategoriesForTesting();

// Clear company categories for testing
await WeeklySyncTester.clearCompanyCategoriesForTesting();

// Simulate old sync date
await WeeklySyncTester.simulateOldCompanyCategorySyncDate();

// Get comprehensive sync info
await WeeklySyncTester.getSyncInfo();
```

### Testing Scenarios:

1. **Fresh Install**: No company categories → triggers sync
2. **Recent Sync**: Data exists, <7 days → skips sync
3. **Stale Data**: Data exists, ≥7 days → triggers sync
4. **Force Sync**: Manual override for testing

## Integration Points

### Database Migration
- Schema version updated from 3 to 4
- Migration adds `company_categories` table
- Backward compatible with existing data

### Background Sync Initialization
- All three sync tasks initialized: categories, companies, company_categories
- Executed in parallel for efficiency
- Individual progress tracking for each task

### UI Integration
- `useAllSyncStatus()` hook provides comprehensive sync status
- Progress calculation includes all sync tasks
- Individual sync status available for each task

## Benefits

1. **Efficient Data Management**: Avoids unnecessary API calls
2. **Relationship Mapping**: Enables local company-category queries
3. **Consistent Pattern**: Follows same weekly interval as companies
4. **Scalable Architecture**: Easy to add more relationship syncs
5. **Offline Capability**: Local data available without network

## Usage Examples

### Get Company Categories for a Company:
```typescript
const companyCategories = await companyCategoryRepository.getByCompanyId(123);
```

### Get Companies for a Category:
```typescript
const companyCategories = await companyCategoryRepository.getByCategoryId(456);
```

### Force Sync for Testing:
```typescript
await backgroundSyncManager.forceSyncCompanyCategories();
```

### Check Sync Status:
```typescript
const { companyCategories } = useAllSyncStatus();
console.log(`Status: ${companyCategories.status}, Progress: ${companyCategories.progress}%`);
```

## Configuration

### Sync Interval
To modify the 7-day interval, update `shouldSyncCompanyCategories()`:

```typescript
const oneWeekAgo = new Date();
oneWeekAgo.setDate(oneWeekAgo.getDate() - 14); // Change to 14 days
```

### Batch Size
Adjust batch processing in repository:

```typescript
const chunkSize = 250; // Modify as needed
```

## Monitoring

### Logs to Monitor:
- `[BackgroundSync] No company categories found locally, sync needed`
- `[BackgroundSync] More than a week since last company categories sync, sync needed`
- `[BackgroundSync] Company categories are up to date, skipping sync`
- `[CompanyCategoryAPI] Fetched X company-category relationships`

### Error Handling:
- Network failures: Automatic retry with exponential backoff
- Data conflicts: Handled by `batchCreateOrUpdate()` method
- Sync failures: Marked as failed with error message stored

## Future Enhancements

1. **Incremental Sync**: Only sync changed relationships
2. **Conflict Resolution**: Handle concurrent data modifications
3. **Selective Sync**: Sync specific company or category relationships
4. **Cache Optimization**: Implement smarter caching strategies
