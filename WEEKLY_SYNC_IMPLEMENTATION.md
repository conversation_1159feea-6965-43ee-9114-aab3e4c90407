# Weekly Company Sync Implementation

## Overview

This implementation adds a 1-week interval for company data synchronization to avoid unnecessary API calls on every app launch. The system now checks if companies are already stored locally and if a week has passed since the last successful sync before making API calls.

## Changes Made

### 1. Company Repository Enhancement

**File**: `src/database/watermelon/repositories/companyRepository.ts`

- Added `getCount()` method to check if companies exist locally
- This method returns the number of companies in the local database

### 2. Background Sync Manager Updates

**File**: `src/services/backgroundSyncManager.ts`

#### New Methods:

- `shouldSyncCompanies()`: Determines if company sync is needed based on:
  - Local company count (sync if 0 companies)
  - Last sync date (sync if more than 7 days ago)
  - Missing sync state (sync if no previous sync found)

- `forceSyncCompanies()`: Forces a company sync regardless of the 1-week rule (useful for testing/manual refresh)

- `getLastCompanySyncDate()`: Returns the date of the last successful company sync

#### Modified Methods:

- `initializeSyncState()`: Now checks if company sync is needed before setting status to 'pending'
- `executeSyncTask()`: Added special handling for companies to check sync necessity before execution

## Logic Flow

### On App Launch:

1. **Initialize Background Sync Manager**
   - Check if companies exist locally (`getCount()`)
   - Check last sync date from sync state
   - If no companies OR more than 7 days since last sync → proceed with sync
   - If companies exist AND less than 7 days → mark as completed, skip sync

2. **Sync Decision Matrix**:
   ```
   Local Companies | Last Sync Date | Action
   0               | Any           | Sync (no data)
   >0              | None          | Sync (no previous sync)
   >0              | <7 days ago   | Skip (data is fresh)
   >0              | ≥7 days ago   | Sync (data is stale)
   ```

### Category Sync:
- **No changes** - Categories continue to sync normally as requested

## Testing

### Using the Test Utility

A test utility has been created at `src/utils/testWeeklySync.ts` with the following methods:

```typescript
import WeeklySyncTester from '../utils/testWeeklySync';

// Test current sync logic
await WeeklySyncTester.testWeeklySync();

// Get detailed sync information
await WeeklySyncTester.getSyncInfo();

// Force sync for testing
await WeeklySyncTester.forceSyncForTesting();

// Clear companies to test initial sync
await WeeklySyncTester.clearCompaniesForTesting();

// Simulate old sync date
await WeeklySyncTester.simulateOldSyncDate();
```

### Manual Testing Scenarios:

1. **Fresh Install Test**:
   ```typescript
   // Clear all companies
   await WeeklySyncTester.clearCompaniesForTesting();
   // Restart app - should trigger sync
   ```

2. **Recent Sync Test**:
   ```typescript
   // Force sync first
   await WeeklySyncTester.forceSyncForTesting();
   // Restart app - should skip sync
   ```

3. **Old Sync Test**:
   ```typescript
   // Simulate old sync date
   await WeeklySyncTester.simulateOldSyncDate();
   // Restart app - should trigger sync
   ```

## Benefits

1. **Reduced API Calls**: Avoids unnecessary API calls when data is fresh
2. **Improved Performance**: Faster app startup when sync is skipped
3. **Bandwidth Savings**: Reduces data usage for users
4. **Server Load Reduction**: Less load on the API server
5. **Configurable**: Easy to modify the 7-day interval if needed

## Configuration

To change the sync interval, modify the date calculation in `shouldSyncCompanies()`:

```typescript
// Change from 7 days to desired interval
oneWeekAgo.setDate(oneWeekAgo.getDate() - 7); // Current: 7 days
oneWeekAgo.setDate(oneWeekAgo.getDate() - 14); // Example: 14 days
```

## Monitoring

The implementation includes comprehensive logging:

- `[BackgroundSync] No companies found locally, sync needed`
- `[BackgroundSync] More than a week since last sync, sync needed`
- `[BackgroundSync] Companies are up to date, skipping sync`

Monitor these logs to verify the sync behavior is working as expected.

## Backward Compatibility

- Existing sync functionality for categories remains unchanged
- First-time users will still get the full company sync
- Users with existing data will benefit from the weekly interval
- Manual force sync option available for edge cases

## Error Handling

- If there's an error checking sync necessity, the system defaults to syncing (fail-safe approach)
- All existing error handling and retry logic remains intact
- Sync state tracking continues to work as before
