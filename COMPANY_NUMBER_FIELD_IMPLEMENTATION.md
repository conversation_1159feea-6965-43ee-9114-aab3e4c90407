# Company Number Field Implementation

## Overview

Added a new `number` field to the companies table to store phone numbers from the API response. This field is now stored in the local database and displayed in the UI with clickable phone call functionality.

## Changes Made

### 1. Database Schema Updates

#### Schema Version

- Already at version 6 with `number` field included
- Migration from version 5 to 6 adds the `number` column

#### Database Structure

```sql
companies:
- id (string, WatermelonDB primary key)
- company_id (number, Original API companyId)
- company_name (string)
- parent_company (string, optional)
- company_email (string, optional)
- company_logo_url (string, optional)
- company_country (string, optional)
- company_address (string, optional)
- company_website (string, optional)
- number (string, optional) ← Phone number field
- is_whatsapp (string, optional) ← Whatsapp number field
- upvote_count (number)
- downvote_count (number)
- created_at (number)
- updated_at (number)
```

### 2. API Interface Updates

#### ApiCompany Interface

```typescript
export interface ApiCompany {
  companyId: number;
  companyName: string;
  // ... other fields
  number?: string | null; // Phone number field
  // ... other fields
}
```

### 3. Repository Updates

#### CompanyData Interface

```typescript
export interface CompanyData {
  id?: string; // WatermelonDB ID (string)
  company_id?: number; // Original API companyId (number)
  company_name: string;
  // ... other fields
  number?: string; // Phone number field
  // ... other fields
}
```

#### Updated Repository Methods

- **`getAll()`**: Returns `number` field for all companies
- **`getById()`**: Returns `number` field for specific company
- **`getByCompanyId()`**: Returns `number` field when querying by API companyId
- **`create()`**: Stores `number` field when creating companies
- **`update()`**: Updates `number` field when modifying companies
- **`batchCreate()`**: Handles `number` field in batch operations

### 4. Background Sync Updates

#### Company Sync Mapping

```typescript
const companyDataList: CompanyData[] = allCompanies.map(apiCompany => ({
  company_id: apiCompany.companyId || 0,
  company_name: apiCompany.companyName || '',
  // ... other fields
  number: apiCompany.number || '', // Store phone number
  // ... other fields
}));
```

### 5. UI Integration

#### CustomCategoriesDetailCard Component

- **Phone Display**: Shows phone number with phone icon when available
- **Clickable Functionality**: Taps on phone number initiate phone calls
- **Conditional Rendering**: Only displays phone section when number exists

```typescript
{
  companyData?.number && (
    <View style={styles.phoneContainer}>
      <Image style={styles.phoneIcon} source={Images.ic_phoneCall} />
      <TouchableOpacity
        style={styles.phoneNumberTouchable}
        onPress={() =>
          companyData.number && handlePhoneCall(companyData.number)
        }>
        <Text style={styles.phoneNumber}>{companyData.number}</Text>
      </TouchableOpacity>
    </View>
  );
}
```

#### Updated Screens

- **CompanyDetailsScreen**: Includes `number` in CompanyDetails interface and data mapping
- **HistoryListScreen**: Passes `number` field to CustomCategoriesDetailCard
- **CategoriesDetailsScreen**: Already includes `number` in Company interface

## API Response Mapping

### API Response Example

```json
{
  "companyId": 13138,
  "companyName": "My paging nation",
  "parentCompany": null,
  "companyEmail": null,
  "companyLogoUrl": null,
  "companyCountry": null,
  "companyAddress": null,
  "companyWebsite": "https://stpi.in",
  "number": "(*************",
  "isWhatsapp": false,
  "upVoteCount": 0,
  "downVoteCount": 0,
  "createdAt": "Wed, 07 May 2025 06:27:48 GMT",
  "categories": [...]
}
```

### Database Mapping

```typescript
{
  company_id: 13138,
  company_name: "My paging nation",
  company_website: "https://stpi.in",
  number: "(*************", // ← Phone number stored
  // ... other fields
}
```

## UI Features

### Phone Number Display

- **Icon**: Phone icon displayed next to number
- **Styling**: Blue underlined text indicating clickability
- **Font**: Poppins-Medium, 17px font size
- **Color**: #0066cc (blue)

### Phone Call Functionality

```typescript
const handlePhoneCall = (phoneNumber: string | number) => {
  const cleanNumber = phoneNumber.toString().replace(/[^\d+]/g, '');
  const phoneUrl = `tel:${cleanNumber}`;

  Linking.canOpenURL(phoneUrl)
    .then(supported => {
      if (supported) {
        return Linking.openURL(phoneUrl);
      } else {
        Alert.alert('Error', 'Phone calls are not supported on this device');
      }
    })
    .catch(err => console.error('Error opening phone app:', err));
};
```

### Conditional Rendering

- Phone number section only appears when `companyData?.number` exists
- Graceful handling of null/undefined values
- No UI disruption when number is not available

## Benefits

### 1. **Enhanced User Experience**

- Direct phone call functionality from company listings
- Visual indication of available contact methods
- Consistent phone number display across all screens

### 2. **Data Completeness**

- Stores complete company contact information
- Maintains API data integrity
- Supports offline access to phone numbers

### 3. **Improved Accessibility**

- Large, tappable phone number area
- Clear visual indicators (phone icon + underlined text)
- Platform-appropriate phone call handling

### 4. **Consistent Implementation**

- Same phone number display across all company cards
- Unified styling and behavior
- Proper error handling for unsupported devices

## Usage Examples

### Display Company with Phone Number

```typescript
<CustomCategoriesDetailCard
  companyData={{
    companyId: 13138,
    companyName: 'My paging nation',
    number: '(*************', // Phone number displayed and clickable
    // ... other fields
  }}
/>
```

### Access Phone Number from Database

```typescript
const company = await watermelonCompanyRepository.getByCompanyId(13138);
if (company?.number) {
  console.log(`Company phone: ${company.number}`);
  // Phone number available for display or calling
}
```

### Background Sync with Phone Numbers

```typescript
// During sync, phone numbers are automatically stored
await backgroundSyncManager.forceSyncCompanies();

// Verify phone numbers are stored
const companies = await watermelonCompanyRepository.getAll();
companies.forEach(company => {
  if (company.number) {
    console.log(`${company.company_name}: ${company.number}`);
  }
});
```

## Testing

### Verify Phone Number Storage

```typescript
// Test shows phone numbers in sample data
await WeeklySyncTester.forceSyncForTesting();
await WeeklySyncTester.getSyncInfo();
// Output: "Sample company: ID=..., CompanyID=..., Name=..., Number=(*************"
```

### Test Phone Call Functionality

1. **Sync companies** to get phone numbers from API
2. **Navigate to company listings** (Categories → Company List)
3. **Verify phone display** - numbers should appear with phone icon
4. **Test phone calls** - tap on phone numbers to initiate calls
5. **Check history** - phone numbers should appear in history listings

### Verify Data Integrity

```typescript
// Check that phone numbers are properly stored and retrieved
const company = await companyRepository.getByCompanyId(13138);
console.log('Phone number:', company?.number); // Should show "(*************"
```

## Migration Notes

### Automatic Migration

- Existing installations automatically migrate to version 6
- New `number` column added with default empty string value
- Next company sync populates phone numbers from API

### Data Population

- Phone numbers populated during next background sync
- Use `forceSyncCompanies()` for immediate phone number update
- Existing companies without phone numbers show no phone section

## Backward Compatibility

### Existing Functionality

- All existing features continue to work unchanged
- Phone number display is additive (doesn't break existing UI)
- Graceful handling when phone numbers are not available

### Optional Field

- Phone number is optional in all interfaces
- UI conditionally renders phone section
- No errors when phone numbers are missing

## Future Enhancements

### Multiple Phone Numbers

```typescript
// Future: Support for multiple phone types
interface CompanyData {
  phone_numbers?: {
    primary?: string;
    toll_free?: string;
    international?: string;
  };
}
```

### Enhanced Phone Features

```typescript
// Future: WhatsApp integration, SMS support
const handleWhatsApp = (phoneNumber: string) => {
  const whatsappUrl = `whatsapp://send?phone=${phoneNumber}`;
  Linking.openURL(whatsappUrl);
};
```

This implementation provides a complete phone number solution that enhances user experience while maintaining data integrity and backward compatibility.
