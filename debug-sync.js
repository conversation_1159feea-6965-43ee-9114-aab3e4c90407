// Debug script to check and restart stuck syncs
// Run this in the React Native debugger console or add it to a component temporarily

const debugSync = async () => {
  try {
    // Import the debug utility
    const { DebugSync } = require('./src/utils/debugSync');
    
    console.log('=== SYNC DEBUG REPORT ===');
    
    // Check all sync statuses
    const statuses = await DebugSync.getAllSyncStatus();
    
    // Check for stuck syncs
    const stuckSyncs = await DebugSync.checkStuckSyncs();
    
    if (stuckSyncs.length > 0) {
      console.log('\n=== AUTO-FIXING STUCK SYNCS ===');
      const fixedCount = await DebugSync.autoFixStuckSyncs();
      console.log(`Fixed ${fixedCount} stuck syncs`);
    }
    
    console.log('\n=== DEBUG COMPLETE ===');
    
  } catch (error) {
    console.error('Debug sync error:', error);
  }
};

// Export for use
if (typeof module !== 'undefined') {
  module.exports = { debugSync };
}

// Auto-run if this file is executed directly
if (typeof window !== 'undefined') {
  // Browser environment - make available globally
  window.debugSync = debugSync;
  console.log('Debug sync function available as window.debugSync()');
} else if (typeof global !== 'undefined') {
  // Node environment - make available globally
  global.debugSync = debugSync;
  console.log('Debug sync function available as global.debugSync()');
}
