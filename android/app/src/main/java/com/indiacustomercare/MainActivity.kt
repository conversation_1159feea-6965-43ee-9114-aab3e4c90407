package com.indiacustomercare

import android.content.Intent
import android.os.Bundle
import org.devio.rn.splashscreen.SplashScreen
import com.facebook.react.ReactActivity
import com.facebook.react.ReactActivityDelegate
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.fabricEnabled
import com.facebook.react.defaults.DefaultReactActivityDelegate
import com.facebook.react.modules.core.DeviceEventManagerModule


class MainActivity : ReactActivity() {

  /**
   * Returns the name of the main component registered from JavaScript. This is used to schedule
   * rendering of the component.
   */
   
   override fun onCreate(savedInstanceState: Bundle?) {
        SplashScreen.show(this)
        super.onCreate(savedInstanceState)
        handleIntent(intent)
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        setIntent(intent)
        handleIntent(intent)
    }

    private fun handleIntent(intent: Intent?) {
        android.util.Log.d("MainActivity", "handleIntent called with action: ${intent?.action}, data: ${intent?.data}")

        // Handle deep link from URL scheme (primary method)
        val data = intent?.data
        if (data != null && data.scheme == "indiacustomercare") {
            android.util.Log.d("MainActivity", "Found deep link URI: $data")
            sendDeepLinkToReactNative(data.toString())
            return
        }

        // Handle deep link from intent extras (fallback for older shortcuts)
        val deepLinkExtra = intent?.getStringExtra("deepLink")
        if (deepLinkExtra != null) {
            android.util.Log.d("MainActivity", "Found deep link extra: $deepLinkExtra")
            sendDeepLinkToReactNative(deepLinkExtra)
            return
        }

        android.util.Log.d("MainActivity", "No deep link found in intent")
    }

    private fun sendDeepLinkToReactNative(deepLink: String) {
        android.util.Log.d("MainActivity", "sendDeepLinkToReactNative called with: $deepLink")
        val reactInstanceManager = reactNativeHost.reactInstanceManager
        val reactContext = reactInstanceManager.currentReactContext

        if (reactContext != null) {
            android.util.Log.d("MainActivity", "React context available, sending deep link immediately")
            reactContext
                .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
                .emit("deepLink", deepLink)
        } else {
            // Store the deep link to send later when React Native is ready
            android.util.Log.d("MainActivity", "React context not ready, storing pending deep link")
            pendingDeepLink = deepLink
        }
    }

    private var pendingDeepLink: String? = null

    override fun onResume() {
        super.onResume()
        // Send pending deep link if React Native is now ready
        pendingDeepLink?.let { deepLink ->
            android.util.Log.d("MainActivity", "onResume: sending pending deep link: $deepLink")
            sendDeepLinkToReactNative(deepLink)
            pendingDeepLink = null
        }
    }

  override fun getMainComponentName(): String = "SourceCode"

  /**
   * Returns the instance of the [ReactActivityDelegate]. We use [DefaultReactActivityDelegate]
   * which allows you to enable New Architecture with a single boolean flags [fabricEnabled]
   */
  override fun createReactActivityDelegate(): ReactActivityDelegate =
      DefaultReactActivityDelegate(this, mainComponentName, fabricEnabled)
}
