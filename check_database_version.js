/**
 * Database Version Checker
 * 
 * This script will help diagnose if the database schema matches the code
 */

import WeeklySyncTester from './src/utils/testWeeklySync';
import database from './src/database/watermelon/database';

async function checkDatabaseVersion() {
  console.log('🔍 Checking Database Version and Schema...\n');
  
  try {
    // Check if we can access the companies table
    console.log('1. Testing companies table access...');
    const companiesCollection = database.get('companies');
    const companyCount = await companiesCollection.query().fetchCount();
    console.log(`   ✅ Companies table exists with ${companyCount} records`);
    
    // Try to create a test company to see which fields are available
    console.log('\n2. Testing company schema fields...');
    
    try {
      await database.write(async () => {
        const testCompany = await companiesCollection.create(company => {
          company.companyId = 999999;
          company.companyName = 'Test Company';
          company.parentCompany = '';
          company.companyEmail = '';
          company.companyLogoUrl = '';
          company.companyCountry = '';
          company.companyAddress = '';
          company.companyWebsite = '';
          
          // This is the field that's causing the error
          try {
            company.number = 'test-number'; // This will fail if field doesn't exist
            console.log('   ✅ number field: EXISTS');
          } catch (error) {
            console.log('   ❌ number field: MISSING');
            throw new Error('number field is missing from database schema');
          }
          
          company.upvoteCount = 0;
          company.downvoteCount = 0;
        });
        
        // Clean up test record
        await testCompany.destroyPermanently();
      });
      
      console.log('   ✅ All required fields exist in database');
      console.log('\n🎉 Database schema is correct!');
      console.log('The error might be caused by something else.');
      
    } catch (error) {
      console.log(`   ❌ Schema validation failed: ${error.message}`);
      console.log('\n🔧 DIAGNOSIS: Database schema is outdated');
      console.log('Your database was created with an older schema version.');
      console.log('The migration to add the "number" field did not run properly.');
      
      return false; // Schema is outdated
    }
    
  } catch (error) {
    console.error('❌ Error checking database:', error);
    return false;
  }
  
  return true; // Schema is correct
}

async function diagnoseAndFix() {
  console.log('🔧 Database Diagnosis and Fix\n');
  
  const isSchemaCorrect = await checkDatabaseVersion();
  
  if (!isSchemaCorrect) {
    console.log('\n' + '='.repeat(50));
    console.log('SOLUTION: Reset Database');
    console.log('='.repeat(50));
    console.log('Your code is correct (schema v6), but your database is outdated.');
    console.log('Run this to fix:');
    console.log('');
    console.log('await WeeklySyncTester.resetDatabaseForSchemaFix();');
    console.log('');
    console.log('This will:');
    console.log('1. Clear the old database');
    console.log('2. Create new tables with schema v6 (including number field)');
    console.log('3. Start fresh sync to populate data');
    
    // Optionally auto-fix
    const autoFix = true; // Set to true to automatically fix
    if (autoFix) {
      console.log('\n🔧 Auto-fixing database...');
      await WeeklySyncTester.resetDatabaseForSchemaFix();
      console.log('✅ Database reset completed! Restart the app.');
    }
  } else {
    console.log('\n✅ Database schema is correct.');
    console.log('The error might be caused by:');
    console.log('1. Data corruption in specific records');
    console.log('2. Race condition during batch operations');
    console.log('3. Memory issues with large datasets');
    console.log('\nTry running a company data reset:');
    console.log('await WeeklySyncTester.resetCompanyDataForSchemaFix();');
  }
}

export { checkDatabaseVersion, diagnoseAndFix };

// If running directly, execute the diagnosis
if (require.main === module) {
  diagnoseAndFix();
}
