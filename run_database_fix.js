/**
 * Quick Database Fix Script
 * 
 * Run this in your React Native app console or add it to a component temporarily
 */

// Add this to any component or run in console:

import WeeklySyncTester from './src/utils/testWeeklySync';

// OPTION 1: Complete database reset (recommended)
const fixDatabase = async () => {
  try {
    console.log('🔧 Resetting database to fix schema error...');
    await WeeklySyncTester.resetDatabaseForSchemaFix();
    console.log('✅ Database reset completed! Restart the app.');
  } catch (error) {
    console.error('Error:', error);
  }
};

// OPTION 2: Reset only company data (if categories are working)
const fixCompanyDataOnly = async () => {
  try {
    console.log('🔧 Resetting company data only...');
    await WeeklySyncTester.resetCompanyDataForSchemaFix();
    console.log('✅ Company data reset completed!');
  } catch (error) {
    console.error('Error:', error);
  }
};

// Call the function you want to use:
// fixDatabase(); // For complete reset
// fixCompanyDataOnly(); // For company data only

export { fixDatabase, fixCompanyDataOnly };
