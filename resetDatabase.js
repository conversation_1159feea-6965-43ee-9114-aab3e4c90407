/**
 * Complete Database Reset Script
 * 
 * This will completely reset the database to start fresh with version 1 schema
 */

import database from './src/database/watermelon/database';
import backgroundSyncManager from './src/services/backgroundSyncManager';

const resetDatabase = async () => {
  console.log('🔄 Starting complete database reset...');
  
  try {
    // Step 1: Reset the database completely
    console.log('1. Resetting database...');
    await database.write(async () => {
      await database.unsafeResetDatabase();
    });
    
    console.log('✅ Database reset completed');
    
    // Step 2: Initialize background sync to repopulate data
    console.log('2. Initializing background sync...');
    await backgroundSyncManager.initialize();
    
    console.log('3. Starting background sync...');
    await backgroundSyncManager.startBackgroundSync();
    
    console.log('✅ Background sync started');
    console.log('\n🎉 Database reset completed successfully!');
    console.log('The app should work normally now. Data will sync in the background.');
    
  } catch (error) {
    console.error('❌ Error resetting database:', error);
    console.log('\n🔄 If this fails, try:');
    console.log('1. Completely close the app');
    console.log('2. Clear app data/cache');
    console.log('3. Restart the app');
  }
};

// Export for use in components or run directly
export default resetDatabase;

// Uncomment the line below to run immediately when this file is imported
// resetDatabase();

console.log('📋 To reset the database, run: resetDatabase()');
