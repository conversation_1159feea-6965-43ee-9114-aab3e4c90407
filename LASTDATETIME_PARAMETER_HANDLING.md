# lastDateTime Parameter Handling Implementation

## Overview

The `lastDateTime` parameter is now conditionally included in API requests based on its value, ensuring cleaner API calls and better server-side handling.

## Implementation Details

### Logic
```typescript
// Only include lastDateTime parameter if it has a valid value (not 0, null, or undefined)
const lastDateTimeParam = (lastDateTime && lastDateTime > 0) ? `&lastDateTime=${lastDateTime}` : '';
```

### Behavior

| lastDateTime Value | Parameter Included | API URL Example |
|-------------------|-------------------|-----------------|
| `0` | ❌ No | `/company/get-all?page=1&limit=500&sortBy=created_at&sortOrder=DESC` |
| `null` | ❌ No | `/company/get-all?page=1&limit=500&sortBy=created_at&sortOrder=DESC` |
| `undefined` | ❌ No | `/company/get-all?page=1&limit=500&sortBy=created_at&sortOrder=DESC` |
| `1749789605` | ✅ Yes | `/company/get-all?page=1&limit=500&sortBy=created_at&sortOrder=DESC&lastDateTime=1749789605` |

## Files Modified

### 1. `src/services/companyApiService.ts`

#### `fetchCompaniesPage()` method:
```typescript
async fetchCompaniesPage(page: number = 1, limit: number = 500, search: string = '', lastDateTime?: number): Promise<CompanyListResponse> {
  try {
    const searchParam = search ? `&search=${encodeURIComponent(search)}` : '';
    
    // Only include lastDateTime parameter if it has a valid value (not 0, null, or undefined)
    const lastDateTimeParam = (lastDateTime && lastDateTime > 0) ? `&lastDateTime=${lastDateTime}` : '';
    
    const url = `/company/get-all?page=${page}&limit=${limit}&sortBy=created_at&sortOrder=DESC${searchParam}${lastDateTimeParam}`;

    console.log(`[CompanyAPI] Fetching companies page ${page} with lastDateTime: ${lastDateTime && lastDateTime > 0 ? lastDateTime : 'not included'}`);
    // ... rest of method
  }
}
```

#### `fetchCompaniesByCategory()` method:
```typescript
async fetchCompaniesByCategory(categoryId: string, page: number = 1, limit: number = 20, lastDateTime?: number): Promise<CompanyListResponse> {
  try {
    // Only include lastDateTime parameter if it has a valid value (not 0, null, or undefined)
    const lastDateTimeParam = (lastDateTime && lastDateTime > 0) ? `&lastDateTime=${lastDateTime}` : '';
    const url = `/company?categoryId=${categoryId}&page=${page}&limit=${limit}&sortBy=created_at&sortOrder=DESC${lastDateTimeParam}`;
    
    console.log(`[CompanyAPI] Fetching companies by category ${categoryId}, page ${page} with lastDateTime: ${lastDateTime && lastDateTime > 0 ? lastDateTime : 'not included'}`);
    // ... rest of method
  }
}
```

### 2. `src/services/backgroundSyncManager.ts`

#### Updated logging:
```typescript
console.log(`[BackgroundSync] Using lastDateTime: ${lastDataTime} (${lastDataTime === 0 ? 'first launch - parameter not included' : new Date(lastDataTime * 1000).toISOString() + ' - parameter included'})`);

console.log(`[BackgroundSync] Fetching companies page ${currentPage}/${totalPages} with lastDateTime=${lastDataTime > 0 ? lastDataTime : 'not included'}...`);
```

## Benefits

1. **Cleaner API Requests**: First-time syncs don't include unnecessary `lastDateTime=0` parameter
2. **Server Differentiation**: Server can easily distinguish between full sync and incremental sync requests
3. **Better Debugging**: Logs clearly indicate when parameter is included vs excluded
4. **Robust Handling**: Handles `null`, `undefined`, and `0` values consistently

## Testing Scenarios

### Scenario 1: First App Launch
- **lastDataTime**: `0` (from sync state)
- **API Call**: `/company/get-all?page=1&limit=500&sortBy=created_at&sortOrder=DESC`
- **Log**: `"Using lastDateTime: 0 (first launch - parameter not included)"`

### Scenario 2: Subsequent Launch
- **lastDataTime**: `1749789605` (saved timestamp)
- **API Call**: `/company/get-all?page=1&limit=500&sortBy=created_at&sortOrder=DESC&lastDateTime=1749789605`
- **Log**: `"Using lastDateTime: 1749789605 (2025-06-13T10:20:05.000Z - parameter included)"`

### Scenario 3: Force Sync
- **lastDataTime**: `0` (reset by force sync)
- **API Call**: `/company/get-all?page=1&limit=500&sortBy=created_at&sortOrder=DESC`
- **Log**: `"Using lastDateTime: 0 (first launch - parameter not included)"`

## Validation

The implementation ensures:
- ✅ `lastDateTime=0` → Parameter not included
- ✅ `lastDateTime=null` → Parameter not included  
- ✅ `lastDateTime=undefined` → Parameter not included
- ✅ `lastDateTime=1749789605` → Parameter included
- ✅ Consistent behavior across both company API methods
- ✅ Clear logging for debugging purposes
