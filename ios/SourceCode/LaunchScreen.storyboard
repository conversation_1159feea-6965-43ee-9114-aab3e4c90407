<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <view key="view" clipsSubviews="YES" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="splash_logo" translatesAutoresizingMaskIntoConstraints="NO" id="uTe-Cq-98X">
                                <rect key="frame" x="20" y="258.5" width="335" height="150"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="150" id="wFU-jW-8MX"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="FAo-zi-18W">
                                <rect key="frame" x="20" y="613.5" width="335" height="33.5"/>
                                <string key="text">© 2025 India Customer Care.
All rights reserved.</string>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Bcu-3y-fUS"/>
                        <color key="backgroundColor" red="0.011355304159999999" green="0.09582392126" blue="0.30084052680000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="FAo-zi-18W" firstAttribute="leading" secondItem="Bcu-3y-fUS" secondAttribute="leading" constant="20" id="65Q-DJ-9d5"/>
                            <constraint firstItem="uTe-Cq-98X" firstAttribute="centerY" secondItem="Ze5-6b-2t3" secondAttribute="centerY" id="abw-YL-CBE"/>
                            <constraint firstItem="Bcu-3y-fUS" firstAttribute="trailing" secondItem="FAo-zi-18W" secondAttribute="trailing" constant="20" id="sBY-qw-URb"/>
                            <constraint firstItem="Bcu-3y-fUS" firstAttribute="trailing" secondItem="uTe-Cq-98X" secondAttribute="trailing" constant="20" id="tLN-BQ-n5D"/>
                            <constraint firstItem="uTe-Cq-98X" firstAttribute="leading" secondItem="Bcu-3y-fUS" secondAttribute="leading" constant="20" id="xU5-zg-qNG"/>
                            <constraint firstItem="Bcu-3y-fUS" firstAttribute="bottom" secondItem="FAo-zi-18W" secondAttribute="bottom" constant="20" id="yVP-iy-ZrU"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="52" y="374.66266866566718"/>
        </scene>
    </scenes>
    <resources>
        <image name="splash_logo" width="1361" height="380"/>
    </resources>
</document>
