import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {View, Text, StyleSheet} from 'react-native';

import Info from '../screens/info/InfoScreen';
import CategoriesScreen from '../screens/categories/CategoriesScreen';
import CategoriesDetailsScreen from '../screens/categoriesDetails/categoriesDetailsScreen';
import CompanyDetailsScreen from '../screens/companyDetails/companyDetailsScreen';
import DrawerNavigator from '../navigation/Drawer';
import commonStyles from '../common/commonStyles';
import {useFirstLaunch} from '../hooks/useFirstLaunch';

const Stack = createNativeStackNavigator();

// Loading component while checking first launch status
const LoadingScreen = () => (
  <View style={styles.loadingContainer}>
    <Text style={styles.loadingText}>Loading...</Text>
  </View>
);

const NavStack = () => {
  const {isFirstLaunch, isLoading} = useFirstLaunch();

  // Show loading screen while checking first launch status
  if (isLoading || isFirstLaunch === null) {
    return <LoadingScreen />;
  }

  // Determine initial route based on first launch status
  const initialRouteName = isFirstLaunch ? 'Info' : 'DrawerNavigator';

  console.log(`[NavStack] Setting initial route to: ${initialRouteName}`);

  return (
    <Stack.Navigator
      initialRouteName={initialRouteName}
      screenOptions={{
        headerStyle: {
          backgroundColor: 'white',
        },
        headerTitleStyle: commonStyles.headerTitle,
        headerShadowVisible: false,
      }}>
      <Stack.Screen
        name="Info"
        component={Info}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="DrawerNavigator"
        component={DrawerNavigator}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="CompanyScreen"
        component={CategoriesDetailsScreen}
        options={{headerShown: true}}
      />
      <Stack.Screen
        name="CompanyDetailsScreen"
        component={CompanyDetailsScreen}
        options={{headerShown: true}}
      />
    </Stack.Navigator>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'Poppins-Regular',
    color: '#333',
  },
});

export default NavStack;
