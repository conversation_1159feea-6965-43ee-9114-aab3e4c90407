// navigation/Drawer.js
import React from 'react';
import {
  TouchableOpacity,
  Image,
  StyleSheet,
  ScrollView,
  View,
} from 'react-native';
import {createDrawerNavigator} from '@react-navigation/drawer';
import {DrawerActions} from '@react-navigation/native';
import CategoriesScreen from '../screens/categories/CategoriesScreen';
import SelectCategoriesScreen from '../screens/selectCategories/SelectCategoriesScreen';
import historyListScreen from '../screens/history/historyListScreen';
import reportScreen from '../screens/reportOrFeedback/reportScreen';
import termsScreen from '../screens/terms/termsScreen';
import privacyPolicyScreen from '../screens/privacyPolicy/privacyPolicyScreen';
import commonStyles from '../common/commonStyles';

import {Images} from '../assets';
import {COLORS, FONTS} from '../common/constant';
import CustomDrawerContent from '../components/CustomDrawerContent';

const Drawer = createDrawerNavigator();

const DrawerIcon = ({navigation}) => (
  <TouchableOpacity
    onPress={() => navigation.dispatch(DrawerActions.openDrawer())}
    style={styles.menuButton}>
    <Image source={Images.menu} style={styles.menuIcon} />
  </TouchableOpacity>
);

export default function DrawerNavigator() {
  return (
    <Drawer.Navigator
      screenOptions={({navigation}) => ({
        headerLeft: () => <DrawerIcon navigation={navigation} />,
        headerShown: true,
        headerStyle: {backgroundColor: 'white'},
        headerTitleStyle: commonStyles.headerTitle,
        headerShadowVisible: false,
        drawerStyle: {
          backgroundColor: COLORS.DRAWER_BG,
          width: '85%',
        },
      })}
      drawerContent={props => <CustomDrawerContent {...props} />}>
      <Drawer.Screen
        name="Categories"
        component={CategoriesScreen}
        options={{title: 'Categories'}}
      />
      <Drawer.Screen
        name="SelectCategories"
        component={SelectCategoriesScreen}
        options={{title: 'Preference', headerTitle: ' '}}
      />
      <Drawer.Screen
        name="HistoryListScreen"
        component={historyListScreen}
        options={{title: 'History'}}
      />
      <Drawer.Screen
        name="ReportScreen"
        component={reportScreen}
        options={{title: 'Report/Feedback'}}
      />
      <Drawer.Screen
        name="TermsScreen"
        component={termsScreen}
        options={{title: 'Terms of Service'}}
      />
      <Drawer.Screen
        name="PrivacyPolicyScreen"
        component={privacyPolicyScreen}
        options={{title: 'Privacy Policy'}}
      />
    </Drawer.Navigator>
  );
}

const styles = StyleSheet.create({
  menuIcon: {
    marginLeft: 15,
    width: 30,
    height: 30,
  },
});
