import React, {useState} from 'react';
import {
  View,
  ActivityIndicator,
  Modal,
  StyleSheet,
  Text,
  TouchableOpacity,
} from 'react-native';

type LoaderProps = {
  visible: boolean;
  text?: string;
};

const Loader = ({visible, text = 'Loading...'}: LoaderProps) => {
  return (
    <View style={styles.container}>
      <Modal transparent={true} visible={visible}>
        <View style={styles.modalContainer}>
          <ActivityIndicator size="large" color="#0000ff" />
          <Text style={styles.loadingText}>{text}</Text>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  loadingText: {
    color: '#fff',
    marginTop: 10,
  },
});

export default Loader;
