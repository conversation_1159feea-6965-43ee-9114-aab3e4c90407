import {NativeStackNavigationOptions} from '@react-navigation/native-stack';
import {Platform} from 'react-native';

export const CONFIG = {
  API_URL: 'https://india-customer-care-api.apps.openxcell.dev/app/v1', // development server
  DEVICE_TYPE: Platform.OS === 'ios' ? 'IOS' : 'ANDROID',
  APP_VERSION: '1.0',
};

export const IOS = Platform.OS === 'ios';

export const FONTS = {
  POPPINS: {
    REGULAR: 'Poppins-Regular',
    ITALIC: 'Poppins-Italic',
    MEDIUM: 'Poppins-Medium',
    MEDIUM_ITALIC: 'Poppins-MediumItalic',
    SEMI_BOLD: 'Poppins-Bold',
    SEMI_BOLD_ITALIC: 'Poppins-SemiBoldItalic',
    EXTRA_BOLD: 'Poppins-ExtraBold',
    EXTRA_BOLD_ITALIC: 'Poppins-ExtraBoldItalic',
    LIGHT: 'Poppins-Light',
    LIGHT_ITALIC: 'Poppins-LightItalic',
    THIN: 'Poppins-Thin',
    THIN_ITALIC: 'Poppins-ThinItalic',
    EXTRA_LIGHT: 'Poppins-ExtraLight',
    EXTRA_LIGHT_ITALIC: 'Poppins-ExtraLightItalic',
    BLACK: 'Poppins-Black',
    BLACK_ITALIC: 'Poppins-BlackItalic',
    BOLD: 'Poppins-Bold',
    BOLD_ITALIC: 'Poppins-BoldItalic',
  },
};

export const COLORS = {
  BLACK: '#000000',
  WHITE: '#FFFFFF',
  BORDER_COLOR: '#D0D5DD',
  BUTTON: '#0a1d50',
  DRAWER_BG: '#F2F5F9',
  DARK_GRAY: '#242424',
  LIGHT_GRAY: '#494949',
  TAB_LIGHT: '#BABABA',
  RED: '#F80016',
  DARK_RED: '#F8051A',
  BORDER: '#DCDCDC',
  BORDER_LIGHT: '#D9D9D9',
  PLACEHOLDER: '#757575',
  BACKGROUND: '#F3F3F3',
  TRANSPARENT: 'transparent',
  primary: '#007AFF',
  success: '#4ECDC4',
  error: '#FF6B6B',
};

export enum VALIDATION_TYPE {
  REQUIRED = '',
  EMAIL = '[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}',
  NUMBER = '^[0-9]*$',
  PASSWORD = '^.{8,22}$',
  OTP = '^.{4,4}$',
  PHONE_NUMBER = '[0-9]{8,13}$',
  USERNAME = '^([A-Za-z0-9](?:(?:[A-Za-z0-9_]|(?:\\.(?!\\.))){0,22}(?:[A-Za-z0-9_.]))?)$',
  WEBSITE = '^(http:\\/\\/www\\.|https:\\/\\/www\\.|http:\\/\\/|https:\\/\\/)?[A-Za-z0-9]+([\\-\\.]{1}[A-Za-z0-9]+)*\\.[A-Za-z]{2,10}(:[0-9]{1,10})?(\\/.*)?$',
}
