import React, {useState} from 'react';
import {View, Text, TouchableOpacity, StyleSheet, Alert, ScrollView} from 'react-native';
import HistoryMigrationFixer from '../utils/fixHistoryMigration';

const HistoryMigrationFixPanel = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [diagnosticsResults, setDiagnosticsResults] = useState<string>('');

  const runDiagnostics = async () => {
    setIsLoading(true);
    setDiagnosticsResults('Running diagnostics...');
    
    try {
      // Capture console logs
      const originalLog = console.log;
      let logOutput = '';
      
      console.log = (...args) => {
        logOutput += args.join(' ') + '\n';
        originalLog(...args);
      };
      
      await HistoryMigrationFixer.runDiagnostics();
      
      // Restore console.log
      console.log = originalLog;
      
      setDiagnosticsResults(logOutput);
    } catch (error) {
      setDiagnosticsResults(`❌ Diagnostics failed: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const checkColumnExists = async () => {
    setIsLoading(true);
    try {
      const exists = await HistoryMigrationFixer.checkNumberIdColumnExists();
      Alert.alert(
        'Column Check Result',
        `number_id column exists: ${exists ? 'YES' : 'NO'}`,
      );
      setDiagnosticsResults(`number_id column exists: ${exists}`);
    } catch (error) {
      Alert.alert('Error', `Failed to check column: ${error}`);
      setDiagnosticsResults(`❌ Failed: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const resetDatabase = async () => {
    Alert.alert(
      'Reset Database',
      'This will clear ALL data and force migrations to run. Are you sure?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: async () => {
            setIsLoading(true);
            try {
              await HistoryMigrationFixer.forceDatabaseReset();
              Alert.alert(
                'Success',
                'Database reset completed. Please restart the app to apply migrations.',
              );
              setDiagnosticsResults('✅ Database reset completed. Restart app to apply migrations.');
            } catch (error) {
              Alert.alert('Error', `Failed to reset database: ${error}`);
              setDiagnosticsResults(`❌ Reset failed: ${error}`);
            } finally {
              setIsLoading(false);
            }
          },
        },
      ],
    );
  };

  const getSchemaVersion = async () => {
    setIsLoading(true);
    try {
      const version = await HistoryMigrationFixer.getCurrentSchemaVersion();
      Alert.alert('Schema Version', `Current version: ${version}`);
      setDiagnosticsResults(`Current schema version: ${version}`);
    } catch (error) {
      Alert.alert('Error', `Failed to get schema version: ${error}`);
      setDiagnosticsResults(`❌ Failed: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>History Migration Fix Panel</Text>
      <Text style={styles.subtitle}>
        Use this panel to diagnose and fix history migration issues
      </Text>

      <TouchableOpacity
        style={[styles.button, styles.primaryButton, isLoading && styles.buttonDisabled]}
        onPress={runDiagnostics}
        disabled={isLoading}>
        <Text style={styles.buttonText}>Run Full Diagnostics</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.button, isLoading && styles.buttonDisabled]}
        onPress={checkColumnExists}
        disabled={isLoading}>
        <Text style={styles.buttonText}>Check number_id Column</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.button, isLoading && styles.buttonDisabled]}
        onPress={getSchemaVersion}
        disabled={isLoading}>
        <Text style={styles.buttonText}>Get Schema Version</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.button, styles.dangerButton, isLoading && styles.buttonDisabled]}
        onPress={resetDatabase}
        disabled={isLoading}>
        <Text style={[styles.buttonText, styles.dangerButtonText]}>Reset Database (DANGER)</Text>
      </TouchableOpacity>

      {diagnosticsResults ? (
        <View style={styles.resultsContainer}>
          <Text style={styles.resultsTitle}>Results:</Text>
          <ScrollView style={styles.resultsScroll}>
            <Text style={styles.resultsText}>{diagnosticsResults}</Text>
          </ScrollView>
        </View>
      ) : null}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#f5f5f5',
    borderRadius: 10,
    margin: 10,
    maxHeight: 600,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
    color: '#333',
  },
  subtitle: {
    fontSize: 14,
    marginBottom: 20,
    textAlign: 'center',
    color: '#666',
    fontStyle: 'italic',
  },
  button: {
    backgroundColor: '#007bff',
    padding: 12,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#28a745',
  },
  dangerButton: {
    backgroundColor: '#dc3545',
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  dangerButtonText: {
    fontWeight: 'bold',
  },
  resultsContainer: {
    marginTop: 20,
    padding: 15,
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    maxHeight: 300,
  },
  resultsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  resultsScroll: {
    maxHeight: 250,
  },
  resultsText: {
    fontSize: 12,
    color: '#333',
    fontFamily: 'monospace',
    lineHeight: 16,
  },
});

export default HistoryMigrationFixPanel;
