// Temporary test panel for database operations
import React, {useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import databaseCleaner from '../utils/cleanDatabase';
import categoryDebugger from '../utils/debugCategorySync';
import backgroundSyncManager from '../services/backgroundSyncManager';
import numberVoteDebugger from '../utils/debugNumberVotes';
import networkTester from '../utils/networkTest';
import {testBankHoliday, isBankHoliday} from '../utils/bankHolidayUtils';
import PushNotificationTestPanel from './PushNotificationTestPanel';

const DatabaseTestPanel = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);
  const [showPushNotificationPanel, setShowPushNotificationPanel] =
    useState(false);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 19)]); // Keep last 20 logs
  };

  const handleAction = async (
    actionName: string,
    action: () => Promise<void>,
  ) => {
    if (isLoading) return;

    setIsLoading(true);
    addLog(`Starting: ${actionName}`);

    try {
      await action();
      addLog(`✅ Completed: ${actionName}`);
    } catch (error) {
      addLog(`❌ Failed: ${actionName} - ${error}`);
      Alert.alert('Error', `${actionName} failed: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const cleanDatabase = () =>
    handleAction('Clean Database', async () => {
      await databaseCleaner.cleanAllData();
    });

  const resetToFresh = () =>
    handleAction('Reset to Fresh State', async () => {
      await databaseCleaner.resetToFreshState();
    });

  const checkCurrentState = () =>
    handleAction('Check Current State', async () => {
      const state = await databaseCleaner.getCurrentState();
      addLog(`State: ${JSON.stringify(state)}`);
    });

  const debugSync = () =>
    handleAction('Debug Category Sync', async () => {
      const debug = await categoryDebugger.debugCategorySync();
      addLog(`Debug: API=${debug.apiCount}, DB=${debug.dbCount}`);
    });

  const forceSync = () =>
    handleAction('Force Sync Categories', async () => {
      await backgroundSyncManager.executeSync('categories');
    });

  const checkDuplicates = () =>
    handleAction('Check Duplicates', async () => {
      const duplicates = await categoryDebugger.checkForDuplicates();
      addLog(`Duplicates found: ${duplicates.length}`);
    });

  const clearLogs = () => {
    setLogs([]);
  };

  // Bank Holiday Testing Functions
  const testCurrentMonth = () =>
    handleAction('Test Bank Holidays - Current Month', async () => {
      // Capture console.log output
      const originalLog = console.log;
      const logMessages: string[] = [];
      console.log = (...args) => {
        logMessages.push(args.join(' '));
      };

      // Use the new utility function
      const testDate = new Date();
      testBankHoliday(testDate, 492); // Test with Bank category

      // Restore console.log
      console.log = originalLog;

      // Add captured logs
      logMessages.forEach(msg => addLog(msg));
    });

  const testSpecificDate = () =>
    handleAction('Test Bank Holiday - Today', async () => {
      const today = new Date();

      const originalLog = console.log;
      const logMessages: string[] = [];
      console.log = (...args) => {
        logMessages.push(args.join(' '));
      };

      // Use the new utility function
      testBankHoliday(today, 492); // Test with Bank category

      console.log = originalLog;
      logMessages.forEach(msg => addLog(msg));
    });

  const testMultipleDates = () =>
    handleAction('Test Multiple Bank Holiday Dates', async () => {
      const originalLog = console.log;
      const logMessages: string[] = [];
      console.log = (...args) => {
        logMessages.push(args.join(' '));
      };

      // Test multiple dates manually
      const testDates = [
        new Date(2024, 0, 26), // Republic Day
        new Date(2024, 7, 15), // Independence Day
        new Date(2024, 9, 2), // Gandhi Jayanti
        new Date(2025, 5, 14), // June 14, 2025 (2nd Saturday)
      ];

      testDates.forEach(date => {
        testBankHoliday(date, 492);
      });

      console.log = originalLog;
      logMessages.forEach(msg => addLog(msg));
    });

  const testRepublicDay = () =>
    handleAction('Test Republic Day (Jan 26)', async () => {
      const currentYear = new Date().getFullYear();
      const republicDay = new Date(currentYear, 0, 26); // January 26

      const result = isBankHoliday(republicDay);
      addLog(
        `Republic Day ${currentYear}: ${
          result.isHoliday ? result.message : 'Not detected as holiday'
        }`,
      );
    });

  const testIndependenceDay = () =>
    handleAction('Test Independence Day (Aug 15)', async () => {
      const currentYear = new Date().getFullYear();
      const independenceDay = new Date(currentYear, 7, 15); // August 15

      const result = isBankHoliday(independenceDay);
      addLog(
        `Independence Day ${currentYear}: ${
          result.isHoliday ? result.message : 'Not detected as holiday'
        }`,
      );
    });

  // Number Vote Count Testing Functions
  const debugNumberVotesOffline = () =>
    handleAction('Debug Number Votes (Offline)', async () => {
      const originalLog = console.log;
      const logMessages: string[] = [];
      console.log = (...args) => {
        logMessages.push(args.join(' '));
      };

      await numberVoteDebugger.debugNumberVotesOffline();

      console.log = originalLog;
      logMessages.forEach(msg => addLog(msg));
    });

  const debugNumberVotes = () =>
    handleAction('Debug Number Vote Counts', async () => {
      const originalLog = console.log;
      const logMessages: string[] = [];
      console.log = (...args) => {
        logMessages.push(args.join(' '));
      };

      await numberVoteDebugger.debugNumberVotes();

      console.log = originalLog;
      logMessages.forEach(msg => addLog(msg));
    });

  const testCompanyNumbers = () =>
    handleAction('Test Company 34837 Numbers', async () => {
      const originalLog = console.log;
      const logMessages: string[] = [];
      console.log = (...args) => {
        logMessages.push(args.join(' '));
      };

      await numberVoteDebugger.debugCompanyNumbers(34837); // "My paging nation" from your example

      console.log = originalLog;
      logMessages.forEach(msg => addLog(msg));
    });

  const getVoteStatistics = () =>
    handleAction('Get Vote Statistics', async () => {
      const originalLog = console.log;
      const logMessages: string[] = [];
      console.log = (...args) => {
        logMessages.push(args.join(' '));
      };

      await numberVoteDebugger.getVoteStatistics();

      console.log = originalLog;
      logMessages.forEach(msg => addLog(msg));
    });

  // Network Testing Functions
  const testNetworkConnectivity = () =>
    handleAction('Test Network Connectivity', async () => {
      const originalLog = console.log;
      const logMessages: string[] = [];
      console.log = (...args) => {
        logMessages.push(args.join(' '));
      };

      await networkTester.testNetworkConnectivity();

      console.log = originalLog;
      logMessages.forEach(msg => addLog(msg));
    });

  const getNetworkStatus = () =>
    handleAction('Get Network Status', async () => {
      const originalLog = console.log;
      const logMessages: string[] = [];
      console.log = (...args) => {
        logMessages.push(args.join(' '));
      };

      await networkTester.getNetworkStatus();

      console.log = originalLog;
      logMessages.forEach(msg => addLog(msg));
    });

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🧪 Database Test Panel</Text>

      <ScrollView style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, styles.dangerButton]}
          onPress={cleanDatabase}
          disabled={isLoading}>
          <Text style={styles.buttonText}>🧹 Clean Database</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.warningButton]}
          onPress={resetToFresh}
          disabled={isLoading}>
          <Text style={styles.buttonText}>🔄 Reset to Fresh State</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.primaryButton]}
          onPress={checkCurrentState}
          disabled={isLoading}>
          <Text style={styles.buttonText}>📊 Check Current State</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.infoButton]}
          onPress={debugSync}
          disabled={isLoading}>
          <Text style={styles.buttonText}>🔍 Debug Category Sync</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.successButton]}
          onPress={forceSync}
          disabled={isLoading}>
          <Text style={styles.buttonText}>🚀 Force Sync Categories</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.infoButton]}
          onPress={checkDuplicates}
          disabled={isLoading}>
          <Text style={styles.buttonText}>🔍 Check Duplicates</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.secondaryButton]}
          onPress={clearLogs}
          disabled={isLoading}>
          <Text style={styles.buttonText}>🗑️ Clear Logs</Text>
        </TouchableOpacity>

        {/* Bank Holiday Testing Buttons */}
        <Text style={styles.sectionTitle}>🏦 Bank Holiday Testing</Text>

        <TouchableOpacity
          style={[styles.button, styles.bankButton]}
          onPress={testCurrentMonth}
          disabled={isLoading}>
          <Text style={styles.buttonText}>📅 Test Current Month</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.bankButton]}
          onPress={testSpecificDate}
          disabled={isLoading}>
          <Text style={styles.buttonText}>📍 Test Today</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.bankButton]}
          onPress={testMultipleDates}
          disabled={isLoading}>
          <Text style={styles.buttonText}>📊 Test Multiple Dates</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.bankButton]}
          onPress={testRepublicDay}
          disabled={isLoading}>
          <Text style={styles.buttonText}>🇮🇳 Test Republic Day</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.bankButton]}
          onPress={testIndependenceDay}
          disabled={isLoading}>
          <Text style={styles.buttonText}>🇮🇳 Test Independence Day</Text>
        </TouchableOpacity>

        {/* Number Vote Count Testing Buttons */}
        <Text style={styles.sectionTitle}>📞 Number Vote Testing</Text>

        <TouchableOpacity
          style={[styles.button, styles.numberButton]}
          onPress={debugNumberVotesOffline}
          disabled={isLoading}>
          <Text style={styles.buttonText}>🔍 Debug Number Votes (Offline)</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.numberButton]}
          onPress={debugNumberVotes}
          disabled={isLoading}>
          <Text style={styles.buttonText}>
            🌐 Debug Number Votes (With API)
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.numberButton]}
          onPress={testCompanyNumbers}
          disabled={isLoading}>
          <Text style={styles.buttonText}>🎯 Test Company 34837 Numbers</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.numberButton]}
          onPress={getVoteStatistics}
          disabled={isLoading}>
          <Text style={styles.buttonText}>📊 Get Vote Statistics</Text>
        </TouchableOpacity>

        {/* Push Notification Testing */}
        <Text style={styles.sectionTitle}>🔔 Push Notification Testing</Text>

        <TouchableOpacity
          style={[styles.button, styles.pushButton]}
          onPress={() =>
            setShowPushNotificationPanel(!showPushNotificationPanel)
          }
          disabled={isLoading}>
          <Text style={styles.buttonText}>
            {showPushNotificationPanel ? '🔽 Hide' : '🔔 Show'} Push
            Notification Tests
          </Text>
        </TouchableOpacity>

        {/* Network Testing Buttons */}
        <Text style={styles.sectionTitle}>🌐 Network Testing</Text>

        <TouchableOpacity
          style={[styles.button, styles.networkButton]}
          onPress={getNetworkStatus}
          disabled={isLoading}>
          <Text style={styles.buttonText}>📊 Get Network Status</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.networkButton]}
          onPress={testNetworkConnectivity}
          disabled={isLoading}>
          <Text style={styles.buttonText}>🔍 Test Network & API</Text>
        </TouchableOpacity>
      </ScrollView>

      <View style={styles.logContainer}>
        <Text style={styles.logTitle}>📝 Logs:</Text>
        <ScrollView style={styles.logScroll}>
          {logs.map((log, index) => (
            <Text key={index} style={styles.logText}>
              {log}
            </Text>
          ))}
          {logs.length === 0 && (
            <Text style={styles.emptyLog}>No logs yet...</Text>
          )}
        </ScrollView>
      </View>

      {isLoading && (
        <View style={styles.loadingOverlay}>
          <Text style={styles.loadingText}>⏳ Processing...</Text>
        </View>
      )}

      <PushNotificationTestPanel visible={showPushNotificationPanel} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
    color: '#333',
  },
  buttonContainer: {
    flex: 1,
    maxHeight: 300,
  },
  button: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  dangerButton: {
    backgroundColor: '#dc3545',
  },
  warningButton: {
    backgroundColor: '#fd7e14',
  },
  primaryButton: {
    backgroundColor: '#007bff',
  },
  successButton: {
    backgroundColor: '#28a745',
  },
  infoButton: {
    backgroundColor: '#17a2b8',
  },
  secondaryButton: {
    backgroundColor: '#6c757d',
  },
  bankButton: {
    backgroundColor: '#e83e8c', // Pink color for bank holiday tests
  },
  numberButton: {
    backgroundColor: '#20c997', // Teal color for number vote tests
  },
  networkButton: {
    backgroundColor: '#fd7e14', // Orange color for network tests
  },
  pushButton: {
    backgroundColor: '#6f42c1', // Purple color for push notification tests
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  logContainer: {
    flex: 1,
    marginTop: 16,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 12,
  },
  logTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  logScroll: {
    flex: 1,
    maxHeight: 200,
  },
  logText: {
    fontSize: 12,
    fontFamily: 'monospace',
    marginBottom: 2,
    color: '#333',
  },
  emptyLog: {
    fontSize: 14,
    fontStyle: 'italic',
    color: '#666',
    textAlign: 'center',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default DatabaseTestPanel;
