import React from 'react';
import { View, Text, TouchableOpacity, Alert, StyleSheet } from 'react-native';
import database from '../database/watermelon/database';
import backgroundSyncManager from '../services/backgroundSyncManager';

const ResetDatabaseButton: React.FC = () => {
  
  const resetDatabase = async () => {
    Alert.alert(
      'Reset Database',
      'This will completely reset the database and re-sync all data. This will fix any schema errors. Continue?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Reset Now', 
          onPress: async () => {
            console.log('🔄 Starting database reset...');
            
            try {
              // Reset the database completely
              await database.write(async () => {
                await database.unsafeResetDatabase();
              });
              
              console.log('✅ Database reset completed');
              
              // Initialize background sync
              await backgroundSyncManager.initialize();
              await backgroundSyncManager.startBackgroundSync();
              
              console.log('✅ Background sync started');
              
              Alert.alert(
                'Database Reset Complete',
                'The database has been reset successfully. Data will sync in the background.',
                [{ text: 'OK' }]
              );
              
            } catch (error) {
              console.error('❌ Error resetting database:', error);
              Alert.alert(
                'Reset Failed', 
                'Could not reset the database. Please restart the app.',
                [{ text: 'OK' }]
              );
            }
          }
        }
      ]
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Database Reset</Text>
      <Text style={styles.description}>
        Reset the database to fix schema errors and start fresh.
      </Text>
      <TouchableOpacity style={styles.button} onPress={resetDatabase}>
        <Text style={styles.buttonText}>Reset Database</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    margin: 10,
    backgroundColor: '#fff3cd',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ffeaa7',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#856404',
  },
  description: {
    fontSize: 14,
    color: '#856404',
    marginBottom: 15,
  },
  button: {
    backgroundColor: '#ffc107',
    padding: 12,
    borderRadius: 6,
    alignItems: 'center',
  },
  buttonText: {
    color: '#212529',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ResetDatabaseButton;
