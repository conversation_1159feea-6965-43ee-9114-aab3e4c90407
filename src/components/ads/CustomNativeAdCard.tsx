import React from 'react';
import {View, Text, Image, TouchableOpacity, StyleSheet} from 'react-native';
import {NativeAd} from 'react-native-google-mobile-ads';

interface Props {
  ad: NativeAd;
}

const CustomNativeAdCard: React.FC<Props> = ({ad}) => {
  return (
    <View style={styles.container}>
      <Text style={styles.headline}>{ad.headline}</Text>

      {ad.icon?.url && (
        <Image
          source={{uri: ad.icon.url}}
          style={styles.icon}
          resizeMode="contain"
        />
      )}

      {ad.body && <Text style={styles.body}>{ad.body}</Text>}

      {ad.advertiser && (
        <Text style={styles.advertiser}>Sponsored by {ad.advertiser}</Text>
      )}

      <TouchableOpacity style={styles.ctaButton}>
        <Text style={styles.ctaText}>{ad.callToAction || 'Learn More'}</Text>
      </TouchableOpacity>
    </View>
  );
};

export default CustomNativeAdCard;

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    padding: 12,
    backgroundColor: '#fff',
    elevation: 2,
    marginVertical: 10,
    marginHorizontal: 15,
  },
  headline: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 6,
  },
  icon: {
    height: 60,
    width: 60,
    marginBottom: 10,
  },
  body: {
    fontSize: 14,
    color: '#555',
    marginBottom: 10,
  },
  advertiser: {
    fontSize: 12,
    color: '#999',
    marginBottom: 8,
  },
  ctaButton: {
    backgroundColor: '#007bff',
    paddingVertical: 8,
    borderRadius: 6,
  },
  ctaText: {
    color: 'white',
    textAlign: 'center',
    fontWeight: '600',
  },
});
