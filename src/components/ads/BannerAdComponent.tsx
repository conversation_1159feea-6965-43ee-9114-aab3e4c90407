import React, { useState } from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { BannerAd, BannerAdSize } from 'react-native-google-mobile-ads';
import adMobService from '../../services/adMobService';

interface BannerAdComponentProps {
  size?: BannerAdSize;
  style?: ViewStyle;
  onAdLoaded?: () => void;
  onAdFailedToLoad?: (error: any) => void;
}

const BannerAdComponent: React.FC<BannerAdComponentProps> = ({
  size = BannerAdSize.BANNER,
  style,
  onAdLoaded,
  onAdFailedToLoad,
}) => {
  const [isAdLoaded, setIsAdLoaded] = useState(false);
  const [adError, setAdError] = useState<any>(null);

  const handleAdLoaded = () => {
    console.log('[BannerAd] Ad loaded successfully');
    setIsAdLoaded(true);
    setAdError(null);
    onAdLoaded?.();
  };

  const handleAdFailedToLoad = (error: any) => {
    console.error('[BannerAd] Failed to load ad:', error);
    setIsAdLoaded(false);
    setAdError(error);
    onAdFailedToLoad?.(error);
  };

  // Don't render if AdMob is not initialized
  if (!adMobService.isAdMobInitialized()) {
    return null;
  }

  return (
    <View style={[styles.container, style]}>
      <BannerAd
        unitId={adMobService.getBannerAdUnitId()}
        size={size}
        requestOptions={{
          requestNonPersonalizedAdsOnly: false,
        }}
        onAdLoaded={handleAdLoaded}
        onAdFailedToLoad={handleAdFailedToLoad}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f5f5f5',
    minHeight: 50,
  },
});

export default BannerAdComponent;
