import React from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { Button } from 'react-native-paper';
import { useInterstitialAd } from '../../hooks/useInterstitialAd';
import { useRewardedAd } from '../../hooks/useRewardedAd';

const AdTestButton: React.FC = () => {
  const { showInterstitialAd, isAdMobReady: interstitialReady } = useInterstitialAd();
  const { showRewardedAd, isAdMobReady: rewardedReady } = useRewardedAd();

  const handleTestInterstitial = async () => {
    if (!interstitialReady) {
      Alert.alert('AdMob Not Ready', 'AdMob is still initializing. Please try again in a moment.');
      return;
    }

    console.log('[AdTestButton] Testing interstitial ad...');
    await showInterstitialAd();
  };

  const handleTestRewarded = async () => {
    if (!rewardedReady) {
      Alert.alert('AdMob Not Ready', 'AdMob is still initializing. Please try again in a moment.');
      return;
    }

    console.log('[AdTestButton] Testing rewarded ad...');
    const rewarded = await showRewardedAd();
    
    if (rewarded) {
      Alert.alert('Reward Earned!', 'You have successfully watched the rewarded ad!');
    } else {
      Alert.alert('No Reward', 'You did not complete watching the rewarded ad.');
    }
  };

  return (
    <View style={styles.container}>
      <Button
        mode="outlined"
        onPress={handleTestInterstitial}
        disabled={!interstitialReady}
        style={styles.button}
        buttonColor="#2196F3"
        textColor="#fff"
      >
        Test Interstitial Ad
      </Button>

      <Button
        mode="outlined"
        onPress={handleTestRewarded}
        disabled={!rewardedReady}
        style={styles.button}
        buttonColor="#4CAF50"
        textColor="#fff"
      >
        Test Rewarded Ad
      </Button>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginVertical: 10,
    paddingHorizontal: 10,
  },
  button: {
    flex: 1,
    marginHorizontal: 5,
  },
});

export default AdTestButton;
