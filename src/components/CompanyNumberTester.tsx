import React from 'react';
import { View, Text, TouchableOpacity, Alert, StyleSheet } from 'react-native';
import watermelonCompanyRepository from '../database/watermelon/repositories/companyRepository';
import backgroundSyncManager from '../services/backgroundSyncManager';

const CompanyNumberTester: React.FC = () => {
  
  const testCompanyNumbers = async () => {
    try {
      console.log('🔍 Testing company numbers...');
      
      // Get first 10 companies
      const companies = await watermelonCompanyRepository.getAll();
      console.log(`Found ${companies.length} total companies`);
      
      let companiesWithNumbers = 0;
      let companiesWithoutNumbers = 0;
      
      companies.slice(0, 10).forEach((company, index) => {
        const hasNumber = company.number && company.number.trim() !== '';
        console.log(`Company ${index + 1}: ${company.company_name} - Number: "${company.number}" - Has Number: ${hasNumber}`);
        
        if (hasNumber) {
          companiesWithNumbers++;
        } else {
          companiesWithoutNumbers++;
        }
      });
      
      console.log(`Summary: ${companiesWithNumbers} with numbers, ${companiesWithoutNumbers} without numbers (from first 10)`);
      
      Alert.alert(
        'Company Numbers Test',
        `Found ${companiesWithNumbers} companies with numbers and ${companiesWithoutNumbers} without numbers (from first 10). Check console for details.`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error testing company numbers:', error);
      Alert.alert('Error', 'Failed to test company numbers. See console for details.');
    }
  };

  const forceSyncAndTest = async () => {
    try {
      Alert.alert(
        'Force Sync and Test',
        'This will re-sync all companies and then test the number fields. Continue?',
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Sync & Test', 
            onPress: async () => {
              console.log('🔄 Force syncing companies...');
              
              // Force sync companies
              await backgroundSyncManager.forceSyncCompanies();
              
              // Wait a bit for sync to start
              setTimeout(async () => {
                await testCompanyNumbers();
              }, 3000);
              
              Alert.alert(
                'Sync Started',
                'Company sync has been started. Numbers will be tested in 3 seconds. Check console logs.',
                [{ text: 'OK' }]
              );
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error force syncing:', error);
      Alert.alert('Error', 'Failed to force sync. See console for details.');
    }
  };

  const checkSpecificCompany = async () => {
    try {
      // Check a specific company by ID (you can change this ID)
      const companyId = 13138; // Example company ID
      const company = await watermelonCompanyRepository.getByCompanyId(companyId);
      
      if (company) {
        console.log(`Specific company check - ID: ${companyId}`);
        console.log(`Name: ${company.company_name}`);
        console.log(`Number: "${company.number}"`);
        console.log(`Has Number: ${!!(company.number && company.number.trim() !== '')}`);
        
        Alert.alert(
          'Specific Company Check',
          `Company: ${company.company_name}\nNumber: "${company.number}"\nHas Number: ${!!(company.number && company.number.trim() !== '')}`,
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert('Not Found', `Company with ID ${companyId} not found in database.`);
      }
    } catch (error) {
      console.error('Error checking specific company:', error);
      Alert.alert('Error', 'Failed to check specific company.');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Company Number Tester</Text>
      <Text style={styles.description}>
        Test if phone numbers are being stored and displayed correctly.
      </Text>
      
      <TouchableOpacity style={styles.button} onPress={testCompanyNumbers}>
        <Text style={styles.buttonText}>Test Company Numbers</Text>
      </TouchableOpacity>
      
      <TouchableOpacity style={[styles.button, styles.secondaryButton]} onPress={checkSpecificCompany}>
        <Text style={styles.buttonText}>Check Specific Company</Text>
      </TouchableOpacity>
      
      <TouchableOpacity style={[styles.button, styles.warningButton]} onPress={forceSyncAndTest}>
        <Text style={styles.buttonText}>Force Sync & Test</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    margin: 10,
    backgroundColor: '#e8f4fd',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#b3d9ff',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#1565c0',
  },
  description: {
    fontSize: 14,
    color: '#1976d2',
    marginBottom: 15,
    lineHeight: 20,
  },
  button: {
    backgroundColor: '#2196f3',
    padding: 12,
    borderRadius: 6,
    alignItems: 'center',
    marginBottom: 10,
  },
  secondaryButton: {
    backgroundColor: '#757575',
  },
  warningButton: {
    backgroundColor: '#ff9800',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default CompanyNumberTester;
