import React from 'react';
import { View, Text, TouchableOpacity, Alert, StyleSheet } from 'react-native';
import watermelonCompanyRepository from '../database/watermelon/repositories/companyRepository';
import backgroundSyncManager from '../services/backgroundSyncManager';

const NumberFieldDebugger: React.FC = () => {
  
  const checkNumberFields = async () => {
    try {
      console.log('🔍 Checking number fields in database...');
      await watermelonCompanyRepository.checkNumberFields();
      
      Alert.alert(
        'Number Field Check',
        'Check the console logs to see the number field data for companies.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error checking number fields:', error);
      Alert.alert('Error', 'Failed to check number fields. See console for details.');
    }
  };

  const forceSyncCompanies = async () => {
    try {
      Alert.alert(
        'Force Sync Companies',
        'This will re-sync all company data including phone numbers. Continue?',
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Sync Now', 
            onPress: async () => {
              console.log('🔄 Force syncing companies...');
              await backgroundSyncManager.forceSyncCompanies();
              
              Alert.alert(
                'Sync Started',
                'Company sync has been started. Check console logs for progress.',
                [{ text: 'OK' }]
              );
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error force syncing companies:', error);
      Alert.alert('Error', 'Failed to start company sync. See console for details.');
    }
  };

  const getCompanyCount = async () => {
    try {
      const count = await watermelonCompanyRepository.getCount();
      Alert.alert(
        'Company Count',
        `There are ${count} companies in the database.`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error getting company count:', error);
      Alert.alert('Error', 'Failed to get company count.');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Number Field Debugger</Text>
      <Text style={styles.description}>
        Debug tools to check if phone numbers are being stored correctly.
      </Text>
      
      <TouchableOpacity style={styles.button} onPress={checkNumberFields}>
        <Text style={styles.buttonText}>Check Number Fields</Text>
      </TouchableOpacity>
      
      <TouchableOpacity style={[styles.button, styles.secondaryButton]} onPress={getCompanyCount}>
        <Text style={styles.buttonText}>Get Company Count</Text>
      </TouchableOpacity>
      
      <TouchableOpacity style={[styles.button, styles.warningButton]} onPress={forceSyncCompanies}>
        <Text style={styles.buttonText}>Force Sync Companies</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    margin: 10,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#dee2e6',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#495057',
  },
  description: {
    fontSize: 14,
    color: '#6c757d',
    marginBottom: 15,
    lineHeight: 20,
  },
  button: {
    backgroundColor: '#007bff',
    padding: 12,
    borderRadius: 6,
    alignItems: 'center',
    marginBottom: 10,
  },
  secondaryButton: {
    backgroundColor: '#6c757d',
  },
  warningButton: {
    backgroundColor: '#ffc107',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default NumberFieldDebugger;
