import React from 'react';
import { View, Text, TouchableOpacity, Alert, StyleSheet } from 'react-native';
import ApiResponseTester from '../utils/apiResponseTester';

const ApiResponseTesterComponent: React.FC = () => {
  
  const testAPIResponses = async () => {
    try {
      Alert.alert(
        'API Response Test',
        'This will test both /company and /company/get-all APIs to compare their response structures. Check console logs for detailed results.',
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Test APIs', 
            onPress: async () => {
              console.log('🚀 Starting API Response Test...');
              await ApiResponseTester.compareAPIResponses();
              
              Alert.alert(
                'Test Complete',
                'API response test completed. Check console logs for detailed comparison results.',
                [{ text: 'OK' }]
              );
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error testing API responses:', error);
      Alert.alert('Error', 'Failed to test API responses. See console for details.');
    }
  };

  const testSpecificCompany = async () => {
    try {
      Alert.prompt(
        'Test Specific Company',
        'Enter a company ID to test the /company/:id endpoint:',
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Test', 
            onPress: async (companyId) => {
              if (companyId && !isNaN(Number(companyId))) {
                console.log(`🔍 Testing company ID: ${companyId}`);
                await ApiResponseTester.testSpecificCompany(Number(companyId));
                
                Alert.alert(
                  'Test Complete',
                  `Company ${companyId} test completed. Check console logs for results.`,
                  [{ text: 'OK' }]
                );
              } else {
                Alert.alert('Invalid Input', 'Please enter a valid company ID number.');
              }
            }
          }
        ],
        'plain-text',
        '33480' // Default company ID
      );
    } catch (error) {
      console.error('Error testing specific company:', error);
      Alert.alert('Error', 'Failed to test specific company. See console for details.');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>API Response Tester</Text>
      <Text style={styles.description}>
        Test and compare API response structures to ensure consistency between endpoints.
      </Text>
      
      <TouchableOpacity style={styles.button} onPress={testAPIResponses}>
        <Text style={styles.buttonText}>Compare API Responses</Text>
      </TouchableOpacity>
      
      <TouchableOpacity style={[styles.button, styles.secondaryButton]} onPress={testSpecificCompany}>
        <Text style={[styles.buttonText, styles.secondaryButtonText]}>Test Specific Company</Text>
      </TouchableOpacity>
      
      <View style={styles.infoContainer}>
        <Text style={styles.infoTitle}>What this tests:</Text>
        <Text style={styles.infoText}>• /company?categoryId=... endpoint</Text>
        <Text style={styles.infoText}>• /company/get-all endpoint</Text>
        <Text style={styles.infoText}>• Number field structure comparison</Text>
        <Text style={styles.infoText}>• Field consistency between APIs</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#f5f5f5',
    margin: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
    lineHeight: 20,
  },
  button: {
    backgroundColor: '#007AFF',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 6,
    marginBottom: 10,
    alignItems: 'center',
  },
  secondaryButton: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButtonText: {
    color: '#007AFF',
  },
  infoContainer: {
    marginTop: 16,
    padding: 12,
    backgroundColor: '#fff',
    borderRadius: 6,
    borderLeftWidth: 4,
    borderLeftColor: '#007AFF',
  },
  infoTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  infoText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
});

export default ApiResponseTesterComponent;
