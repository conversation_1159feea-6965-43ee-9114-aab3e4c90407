import React from 'react';
import {View, StyleSheet} from 'react-native';
import {Text} from 'react-native-paper';
import commonStyles from '../common/commonStyles';

interface BankHolidayBannerProps {
  /** Whether to show the banner (should be true only for bank holidays) */
  isVisible: boolean;
  /** The holiday message to display */
  message: string;
  /** Optional custom background color */
  backgroundColor?: string;
  /** Optional custom text style */
  textStyle?: object;
}

/**
 * Reusable component for displaying bank holiday messages
 * Shows a banner with holiday information for bank category companies/categories
 */
const BankHolidayBanner: React.FC<BankHolidayBannerProps> = ({
  isVisible,
  message,
  backgroundColor = '#F89696',
  textStyle,
}) => {
  if (!isVisible || !message) {
    return null;
  }

  return (
    <View style={[styles.container, {backgroundColor}]}>
      <Text style={[commonStyles.labelTopInstructor, textStyle]}>
        {message}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 4, // Reduced from 8 to 4 for smaller height
    paddingHorizontal: 15,
  },
});

export default BankHolidayBanner;
