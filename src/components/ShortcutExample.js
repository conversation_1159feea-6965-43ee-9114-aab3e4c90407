import React from 'react';
import {View, Text, StyleSheet, Alert, Platform} from 'react-native';
import {useAppShortcuts} from '../hooks/useAppShortcuts';
import ShortcutButton from './ShortcutButton';

/**
 * Example component demonstrating how to use app shortcuts
 * This is a reference implementation showing various shortcut creation methods
 */
const ShortcutExample = ({navigation}) => {
  const {
    isShortcutSupported,
    createCompanyListShortcut,
    createCategoriesShortcut,
    removeShortcut,
    existingShortcuts,
    isLoading,
  } = useAppShortcuts(navigation);

  // Example: Create shortcut for a specific category
  const handleCreateBankShortcut = async () => {
    try {
      const success = await createCompanyListShortcut(2, 'Bank');
      if (success) {
        Alert.alert('Success', 'Bank Companies shortcut created!');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to create shortcut');
    }
  };

  // Example: Create shortcut for insurance category
  const handleCreateInsuranceShortcut = async () => {
    try {
      const success = await createCompanyListShortcut(3, 'Insurance');
      if (success) {
        Alert.alert('Success', 'Insurance Companies shortcut created!');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to create shortcut');
    }
  };

  // Example: Create main categories shortcut
  const handleCreateMainShortcut = async () => {
    try {
      const success = await createCategoriesShortcut();
      if (success) {
        Alert.alert('Success', 'Categories shortcut created!');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to create shortcut');
    }
  };

  // Example: Remove a specific shortcut
  const handleRemoveShortcut = async (shortcutId) => {
    try {
      const success = await removeShortcut(shortcutId);
      if (success) {
        Alert.alert('Success', 'Shortcut removed!');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to remove shortcut');
    }
  };

  // Don't render on iOS
  if (Platform.OS !== 'android') {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>App Shortcuts</Text>
        <Text style={styles.subtitle}>
          App shortcuts are only available on Android devices.
        </Text>
      </View>
    );
  }

  // Show not supported message
  if (!isShortcutSupported) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>App Shortcuts</Text>
        <Text style={styles.subtitle}>
          App shortcuts are not supported on this device.
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>App Shortcuts Demo</Text>
      <Text style={styles.subtitle}>
        Create shortcuts to quickly access specific features
      </Text>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Category Shortcuts</Text>
        
        <ShortcutButton
          title="Add Bank Companies Shortcut"
          onPress={handleCreateBankShortcut}
          isLoading={isLoading}
          style={styles.button}
        />

        <ShortcutButton
          title="Add Insurance Companies Shortcut"
          onPress={handleCreateInsuranceShortcut}
          isLoading={isLoading}
          style={styles.button}
        />

        <ShortcutButton
          title="Add Categories Shortcut"
          onPress={handleCreateMainShortcut}
          isLoading={isLoading}
          style={styles.button}
        />
      </View>

      {existingShortcuts.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Existing Shortcuts</Text>
          {existingShortcuts.map((shortcut) => (
            <View key={shortcut.id} style={styles.shortcutItem}>
              <Text style={styles.shortcutLabel}>
                {shortcut.shortLabel || shortcut.longLabel}
              </Text>
              <ShortcutButton
                title="Remove"
                onPress={() => handleRemoveShortcut(shortcut.id)}
                isLoading={isLoading}
                style={[styles.button, styles.removeButton]}
                textStyle={styles.removeButtonText}
              />
            </View>
          ))}
        </View>
      )}

      <View style={styles.infoSection}>
        <Text style={styles.infoTitle}>How to use shortcuts:</Text>
        <Text style={styles.infoText}>
          1. Tap a button above to create a shortcut
        </Text>
        <Text style={styles.infoText}>
          2. Confirm the shortcut creation when prompted
        </Text>
        <Text style={styles.infoText}>
          3. Find the shortcut on your home screen
        </Text>
        <Text style={styles.infoText}>
          4. Tap the shortcut to quickly access that feature
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333',
  },
  button: {
    marginBottom: 8,
  },
  shortcutItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'white',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    elevation: 1,
  },
  shortcutLabel: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  removeButton: {
    backgroundColor: '#dc3545',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginBottom: 0,
  },
  removeButtonText: {
    fontSize: 12,
  },
  infoSection: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    elevation: 1,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  infoText: {
    fontSize: 14,
    marginBottom: 4,
    color: '#666',
  },
});

export default ShortcutExample;
