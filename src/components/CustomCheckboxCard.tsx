import React from 'react';
import {View, Text, StyleSheet, Pressable, Image} from 'react-native';
import {Images} from '../assets';

interface Props {
  label: string;
  checked: boolean;
  onToggle: () => void;
}

const CustomCheckboxCard = ({label, checked, onToggle}: Props) => {
  return (
    <Pressable style={styles.checkboxContainer} onPress={onToggle}>
      <Image
        source={checked ? Images.checkBoxChecked : Images.checkBoxUnChecked}
        style={styles.checkboxIcon}
      />
      <Text style={styles.label}>{label}</Text>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f1f5f9',
    padding: 12,
    borderRadius: 10,
    marginBottom: 10,
    borderWidth: 1.5,
    borderColor: '#D7E2F1',
  },
  checkboxIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  label: {
    marginLeft: 10,
    fontSize: 18,
    fontFamily: 'Poppins-Medium',
  },
});

export default CustomCheckboxCard;
