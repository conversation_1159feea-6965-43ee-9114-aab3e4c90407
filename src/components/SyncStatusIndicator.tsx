import React from 'react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { useAllSyncStatus } from '../hooks/useBackgroundSync';
import { COLORS } from '../common/constant';

interface SyncStatusIndicatorProps {
  showOnlyWhenActive?: boolean;
}

const SyncStatusIndicator: React.FC<SyncStatusIndicatorProps> = ({ 
  showOnlyWhenActive = true 
}) => {
  const { 
    isAnySyncInProgress, 
    allSyncsCompleted, 
    anySyncFailed, 
    overallProgress,
    categories,
    companies 
  } = useAllSyncStatus();

  // Don't show anything if showOnlyWhenActive is true and no sync is in progress
  if (showOnlyWhenActive && !isAnySyncInProgress) {
    return null;
  }

  const getStatusText = () => {
    if (isAnySyncInProgress) {
      return `Syncing data... ${overallProgress}%`;
    }
    if (allSyncsCompleted) {
      return 'Data sync completed';
    }
    if (anySyncFailed) {
      return 'Sync failed - will retry automatically';
    }
    return 'Preparing to sync...';
  };

  const getStatusColor = () => {
    if (anySyncFailed) return COLORS.error || '#FF6B6B';
    if (allSyncsCompleted) return COLORS.success || '#4ECDC4';
    return COLORS.primary || '#007AFF';
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        {isAnySyncInProgress && (
          <ActivityIndicator 
            size="small" 
            color={getStatusColor()} 
            style={styles.indicator}
          />
        )}
        <Text style={[styles.text, { color: getStatusColor() }]}>
          {getStatusText()}
        </Text>
      </View>
      
      {/* Progress bar */}
      {isAnySyncInProgress && (
        <View style={styles.progressContainer}>
          <View 
            style={[
              styles.progressBar, 
              { 
                width: `${overallProgress}%`,
                backgroundColor: getStatusColor()
              }
            ]} 
          />
        </View>
      )}

      {/* Detailed status for debugging (only show in development) */}
      {__DEV__ && (
        <View style={styles.debugInfo}>
          <Text style={styles.debugText}>
            Categories: {categories.status} ({categories.progress}%)
          </Text>
          <Text style={styles.debugText}>
            Companies: {companies.status} ({companies.progress}%)
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    padding: 12,
    margin: 8,
    borderWidth: 1,
    borderColor: '#E9ECEF',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  indicator: {
    marginRight: 8,
  },
  text: {
    fontSize: 14,
    fontWeight: '500',
  },
  progressContainer: {
    height: 4,
    backgroundColor: '#E9ECEF',
    borderRadius: 2,
    marginTop: 8,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 2,
  },
  debugInfo: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#E9ECEF',
  },
  debugText: {
    fontSize: 12,
    color: '#6C757D',
    marginBottom: 2,
  },
});

export default SyncStatusIndicator;
