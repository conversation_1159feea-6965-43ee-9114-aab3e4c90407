import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
  Platform,
} from 'react-native';
import {COLORS, FONTS} from '../common/constant';

const ShortcutButton = ({
  onPress,
  title = 'Add to Home Screen',
  isLoading = false,
  disabled = false,
  style = {},
  textStyle = {},
}) => {
  // Don't render on iOS
  if (Platform.OS !== 'android') {
    return null;
  }

  return (
    <TouchableOpacity
      style={[styles.button, disabled && styles.buttonDisabled, style]}
      onPress={onPress}
      disabled={disabled || isLoading}
      activeOpacity={0.7}>
      {isLoading ? (
        <ActivityIndicator size="small" color={COLORS.WHITE} />
      ) : (
        <Text style={[styles.buttonText, textStyle]}>{title}</Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    backgroundColor: COLORS.PRIMARY || '#0a1d50',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  buttonDisabled: {
    backgroundColor: '#cccccc',
    elevation: 0,
    shadowOpacity: 0,
  },
  buttonText: {
    color: COLORS.WHITE || '#ffffff',
    fontSize: 14,
    fontFamily: FONTS.MEDIUM || 'Poppins-Medium',
    textAlign: 'center',
  },
});

export default ShortcutButton;
