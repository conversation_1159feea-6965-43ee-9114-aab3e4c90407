import React, { useState } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { Text, Button } from 'react-native-paper';
import { testNotesFeature } from '../utils/testNotesFeature';
import { COLORS, FONTS } from '../common/constant';

interface NotesTestPanelProps {
  visible?: boolean;
}

const NotesTestPanel: React.FC<NotesTestPanelProps> = ({ visible = true }) => {
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState<string | null>(null);

  const runTests = async () => {
    setTesting(true);
    setTestResult(null);
    
    try {
      console.log('Starting Notes Feature Tests...');
      await testNotesFeature();
      setTestResult('✅ All tests passed successfully!');
      Alert.alert('Success', 'All Notes feature tests passed successfully!');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setTestResult(`❌ Tests failed: ${errorMessage}`);
      Alert.alert('Test Failed', `Notes feature tests failed: ${errorMessage}`);
      console.error('Notes feature tests failed:', error);
    } finally {
      setTesting(false);
    }
  };

  if (!visible) {
    return null;
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Notes Feature Test Panel</Text>
      <Text style={styles.description}>
        Test the complete Notes feature including database operations, 
        CRUD operations, and data persistence.
      </Text>
      
      <Button
        mode="contained"
        onPress={runTests}
        disabled={testing}
        style={styles.testButton}
        labelStyle={styles.buttonLabel}
        buttonColor={COLORS.PRIMARY || '#0a1d50'}>
        {testing ? 'Running Tests...' : 'Run Notes Tests'}
      </Button>
      
      {testResult && (
        <View style={styles.resultContainer}>
          <Text style={styles.resultText}>{testResult}</Text>
        </View>
      )}
      
      <View style={styles.infoContainer}>
        <Text style={styles.infoTitle}>Test Coverage:</Text>
        <Text style={styles.infoItem}>• Create notes</Text>
        <Text style={styles.infoItem}>• Read notes by company ID</Text>
        <Text style={styles.infoItem}>• Delete notes (soft delete)</Text>
        <Text style={styles.infoItem}>• Multiple notes handling</Text>
        <Text style={styles.infoItem}>• Notes count functionality</Text>
        <Text style={styles.infoItem}>• Data persistence</Text>
        <Text style={styles.infoItem}>• Database cleanup</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.WHITE || '#ffffff',
    borderWidth: 1,
    borderColor: '#D7E2F1',
    borderRadius: 8,
    margin: 15,
    padding: 15,
  },
  title: {
    fontSize: 18,
    fontFamily: FONTS.POPPINS?.SEMI_BOLD || 'System',
    color: '#000',
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    fontFamily: FONTS.POPPINS?.REGULAR || 'System',
    color: '#666',
    marginBottom: 15,
    lineHeight: 20,
  },
  testButton: {
    marginVertical: 10,
    paddingVertical: 6,
    borderRadius: 8,
  },
  buttonLabel: {
    fontSize: 16,
    fontFamily: FONTS.POPPINS?.MEDIUM || 'System',
  },
  resultContainer: {
    backgroundColor: '#f8f9fa',
    borderRadius: 6,
    padding: 12,
    marginTop: 15,
    borderLeftWidth: 4,
    borderLeftColor: '#28a745',
  },
  resultText: {
    fontSize: 14,
    fontFamily: FONTS.POPPINS?.MEDIUM || 'System',
    color: '#333',
  },
  infoContainer: {
    marginTop: 20,
    padding: 12,
    backgroundColor: '#f1f3f4',
    borderRadius: 6,
  },
  infoTitle: {
    fontSize: 14,
    fontFamily: FONTS.POPPINS?.SEMI_BOLD || 'System',
    color: '#333',
    marginBottom: 8,
  },
  infoItem: {
    fontSize: 12,
    fontFamily: FONTS.POPPINS?.REGULAR || 'System',
    color: '#666',
    marginBottom: 4,
    paddingLeft: 8,
  },
});

export default NotesTestPanel;
