import { createSlice } from "@reduxjs/toolkit";

const initialState = {
    isLoading: false
};

const CommonSlice = createSlice({
    name: "Common",
    initialState,
    reducers: {
        showLoader(state) {
            state.isLoading = true
        },
        hideLoader(state) {
            state.isLoading = false
        }
    }
})


export const { showLoader, hideLoader } = CommonSlice.actions
export default CommonSlice.reducer
