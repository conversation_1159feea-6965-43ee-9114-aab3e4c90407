import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { fetchCategory } from '../api/category';

export const getCategory = createAsyncThunk(
    'category',
    async (_, thunkAPI) => {
        const response: any = await fetchCategory(undefined);
        return response.data;
    }
);

// Create the slice
const categorySlice = createSlice({
  name: 'category',
  initialState: {},
  reducers: {
    setCategory: (state, action) => {
      return action.payload;
    },
  },
  extraReducers: builder => {
    builder.addCase(getCategory.fulfilled, (state, action) => {
      return action.payload;
    });
  },
});

// Export actions and reducer
export const { setCategory } = categorySlice.actions;
export default categorySlice.reducer;