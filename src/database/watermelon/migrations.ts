import { schemaMigrations, createTable, addColumns } from '@nozbe/watermelondb/Schema/migrations';

export default schemaMigrations({
  migrations: [
    // Migration from version 1 to 2: Add numbers table
    {
      toVersion: 2,
      steps: [
        createTable({
          name: 'numbers',
          columns: [
            { name: 'api_number_id', type: 'number' }, // Store original API numberId
            { name: 'company_id', type: 'number' }, // API companyId
            { name: 'number', type: 'string' },
            { name: 'description', type: 'string', isOptional: true },
            { name: 'type', type: 'string' }, // TOLL_FREE, ALL_INDIA, INTERNATIONAL
            { name: 'upvote_count', type: 'number' },
            { name: 'downvote_count', type: 'number' },
            { name: 'is_whatsapp', type: 'number' }, // 0 or 1
            { name: 'created_at', type: 'number' },
            { name: 'updated_at', type: 'number' },
          ],
        }),
      ],
    },
    // Migration from version 2 to 3: Add last_data_time to sync_state table
    {
      toVersion: 3,
      steps: [
        addColumns({
          table: 'sync_state',
          columns: [
            { name: 'last_data_time', type: 'number', isOptional: true }, // Epoch timestamp for lastDateTime API parameter
          ],
        }),
      ],
    },
    // Migration from version 3 to 4: Add category_priority to categories table
    {
      toVersion: 4,
      steps: [
        addColumns({
          table: 'categories',
          columns: [
            { name: 'category_priority', type: 'number' }, // Priority for sorting (lower values appear first)
          ],
        }),
      ],
    },
    // Migration from version 4 to 5: Add company_priority to companies table
    {
      toVersion: 5,
      steps: [
        addColumns({
          table: 'companies',
          columns: [
            { name: 'company_priority', type: 'number' }, // Priority for sorting (lower values appear first)
          ],
        }),
      ],
    },
    // Migration from version 5 to 6: Add notes table
    {
      toVersion: 6,
      steps: [
        createTable({
          name: 'notes',
          columns: [
            { name: 'note_id', type: 'string' }, // Primary key (unique identifier for note)
            { name: 'note_text', type: 'string' }, // The content/text of the note
            { name: 'company_id', type: 'string' }, // Foreign key (reference to company record)
            { name: 'created_at', type: 'number' }, // Timestamp when the note was created
            { name: 'deleted', type: 'boolean' }, // Soft-delete flag for sync safety (default: false)
          ],
        }),
      ],
    },
    // Migration from version 6 to 7: Add reminder fields to notes table
    {
      toVersion: 7,
      steps: [
        addColumns({
          table: 'notes',
          columns: [
            { name: 'reminder_at', type: 'number', isOptional: true }, // Timestamp for reminder notification
            { name: 'notification_id', type: 'string', isOptional: true }, // Push notification ID for cancellation
          ],
        }),
      ],
    },
    // Migration from version 7 to 8: Add company_name and number fields to history table
    {
      toVersion: 8,
      steps: [
        addColumns({
          table: 'history',
          columns: [
            { name: 'company_name', type: 'string', isOptional: true }, // Company name for display
            { name: 'number', type: 'string', isOptional: true }, // Tapped phone number
            { name: 'number_id', type: 'number', isOptional: true }, // API numberId for reference
          ],
        }),
      ],
    },
  ],
});
