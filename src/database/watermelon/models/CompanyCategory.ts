import { Model } from '@nozbe/watermelondb';
import { field, date } from '@nozbe/watermelondb/decorators';

export default class CompanyCategory extends Model {
  static table = 'company_categories';

  @field('api_id') apiId!: number; // Store original API id
  @field('company_id') companyId!: number; // API companyId
  @field('category_id') categoryId!: number; // API categoryId
  @date('created_at') createdAt!: Date;
  @date('updated_at') updatedAt!: Date;
}
