import { Model } from '@nozbe/watermelondb';
import { field, date } from '@nozbe/watermelondb/decorators';

export default class Number extends Model {
  static table = 'numbers';

  @field('api_number_id') apiNumberId!: number; // Store original API numberId
  @field('company_id') companyId!: number; // API companyId
  @field('number') number!: string;
  @field('description') description!: string;
  @field('type') type!: string; // TOLL_FREE, ALL_INDIA, INTERNATIONAL
  @field('upvote_count') upvoteCount!: number;
  @field('downvote_count') downvoteCount!: number;
  @field('is_whatsapp') isWhatsapp!: number; // 0 or 1
  @date('created_at') createdAt!: Date;
  @date('updated_at') updatedAt!: Date;
}
