import { Model } from '@nozbe/watermelondb';
import { field, date } from '@nozbe/watermelondb/decorators';

export default class Note extends Model {
  static table = 'notes';

  @field('note_id') noteId!: string; // Primary key (unique identifier for note)
  @field('note_text') noteText!: string; // The content/text of the note
  @field('company_id') companyId!: string; // Foreign key (reference to company record)
  @date('created_at') createdAt!: Date; // Timestamp when the note was created
  @field('deleted') deleted!: boolean; // Soft-delete flag for sync safety (default: false)
  @date('reminder_at') reminderAt?: Date; // Timestamp for reminder notification
  @field('notification_id') notificationId?: string; // Push notification ID for cancellation
}
