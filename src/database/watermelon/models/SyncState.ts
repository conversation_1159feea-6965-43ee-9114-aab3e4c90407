import { Model } from '@nozbe/watermelondb';
import { field, date } from '@nozbe/watermelondb/decorators';

export default class SyncState extends Model {
  static table = 'sync_state';

  @field('sync_key') syncKey!: string;
  @field('status') status!: string; // 'pending', 'in_progress', 'completed', 'failed'
  @field('progress') progress!: number; // 0-100 percentage
  @date('last_sync_at') lastSyncAt?: Date;
  @field('last_data_time') lastDataTime?: number; // Epoch timestamp for lastDateTime API parameter
  @field('error_message') errorMessage?: string;
  @field('retry_count') retryCount!: number;
  @date('created_at') createdAt!: Date;
  @date('updated_at') updatedAt!: Date;
}
