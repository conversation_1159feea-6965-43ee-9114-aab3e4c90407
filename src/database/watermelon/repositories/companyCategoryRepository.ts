import { Q } from '@nozbe/watermelondb';
import database from '../database';
import CompanyCategory from '../models/CompanyCategory';

export interface CompanyCategoryData {
  id?: string; // WatermelonDB ID (string)
  apiId: number; // Original API id
  companyId: number; // API companyId
  categoryId: number; // API categoryId
  created_at?: Date;
  updated_at?: Date;
}

class WatermelonCompanyCategoryRepository {
  private collection = database.get<CompanyCategory>('company_categories');

  async getAll(): Promise<CompanyCategoryData[]> {
    try {
      const companyCategories = await this.collection.query().fetch();
      return companyCategories.map(companyCategory => ({
        id: companyCategory.id,
        apiId: companyCategory.apiId,
        companyId: companyCategory.companyId,
        categoryId: companyCategory.categoryId,
        created_at: companyCategory.createdAt,
        updated_at: companyCategory.updatedAt,
      }));
    } catch (error) {
      console.error('Error getting company categories:', error);
      throw error;
    }
  }

  async getByCompanyId(companyId: number): Promise<CompanyCategoryData[]> {
    try {
      const companyCategories = await this.collection
        .query(Q.where('company_id', companyId))
        .fetch();
      
      return companyCategories.map(companyCategory => ({
        id: companyCategory.id,
        apiId: companyCategory.apiId,
        companyId: companyCategory.companyId,
        categoryId: companyCategory.categoryId,
        created_at: companyCategory.createdAt,
        updated_at: companyCategory.updatedAt,
      }));
    } catch (error) {
      console.error('Error getting company categories by company ID:', error);
      throw error;
    }
  }

  async getByCategoryId(categoryId: number): Promise<CompanyCategoryData[]> {
    try {
      const companyCategories = await this.collection
        .query(Q.where('category_id', categoryId))
        .fetch();
      
      return companyCategories.map(companyCategory => ({
        id: companyCategory.id,
        apiId: companyCategory.apiId,
        companyId: companyCategory.companyId,
        categoryId: companyCategory.categoryId,
        created_at: companyCategory.createdAt,
        updated_at: companyCategory.updatedAt,
      }));
    } catch (error) {
      console.error('Error getting company categories by category ID:', error);
      throw error;
    }
  }

  async getByApiId(apiId: number): Promise<CompanyCategoryData | null> {
    try {
      const companyCategories = await this.collection
        .query(Q.where('api_id', apiId))
        .fetch();
      
      if (companyCategories.length === 0) {
        return null;
      }

      const companyCategory = companyCategories[0];
      return {
        id: companyCategory.id,
        apiId: companyCategory.apiId,
        companyId: companyCategory.companyId,
        categoryId: companyCategory.categoryId,
        created_at: companyCategory.createdAt,
        updated_at: companyCategory.updatedAt,
      };
    } catch (error) {
      console.error('Error getting company category by API ID:', error);
      return null;
    }
  }

  async getCount(): Promise<number> {
    try {
      const count = await this.collection.query().fetchCount();
      return count;
    } catch (error) {
      console.error('Error getting company categories count:', error);
      throw error;
    }
  }

  async create(companyCategoryData: CompanyCategoryData): Promise<string> {
    try {
      const newCompanyCategory = await database.write(async () => {
        return await this.collection.create(companyCategory => {
          companyCategory.apiId = companyCategoryData.apiId;
          companyCategory.companyId = companyCategoryData.companyId;
          companyCategory.categoryId = companyCategoryData.categoryId;
        });
      });
      return newCompanyCategory.id;
    } catch (error) {
      console.error('Error creating company category:', error);
      throw error;
    }
  }

  async update(companyCategoryData: CompanyCategoryData): Promise<void> {
    if (!companyCategoryData.id) {
      throw new Error('Company category ID is required for update');
    }

    try {
      await database.write(async () => {
        const companyCategory = await this.collection.find(companyCategoryData.id!);
        await companyCategory.update(companyCategory => {
          companyCategory.apiId = companyCategoryData.apiId;
          companyCategory.companyId = companyCategoryData.companyId;
          companyCategory.categoryId = companyCategoryData.categoryId;
        });
      });
    } catch (error) {
      console.error('Error updating company category:', error);
      throw error;
    }
  }

  async delete(id: string): Promise<void> {
    try {
      await database.write(async () => {
        const companyCategory = await this.collection.find(id);
        await companyCategory.destroyPermanently();
      });
    } catch (error) {
      console.error('Error deleting company category:', error);
      throw error;
    }
  }

  async createOrUpdate(companyCategoryData: CompanyCategoryData): Promise<string> {
    const existing = await this.getByApiId(companyCategoryData.apiId);
    if (existing && existing.id) {
      await this.update({ ...companyCategoryData, id: existing.id });
      return existing.id;
    } else {
      return await this.create(companyCategoryData);
    }
  }

  async batchCreate(companyCategoriesData: CompanyCategoryData[]): Promise<void> {
    try {
      // Process in smaller chunks if the batch is too large
      const maxBatchSize = 500; // WatermelonDB batch size limit

      if (companyCategoriesData.length <= maxBatchSize) {
        // Single batch
        await database.write(async () => {
          const batch = companyCategoriesData.map(companyCategoryData =>
            this.collection.prepareCreate(companyCategory => {
              companyCategory.apiId = companyCategoryData.apiId;
              companyCategory.companyId = companyCategoryData.companyId;
              companyCategory.categoryId = companyCategoryData.categoryId;
            })
          );
          await database.batch(batch);
        });
      } else {
        // Multiple batches for large datasets
        for (let i = 0; i < companyCategoriesData.length; i += maxBatchSize) {
          const chunk = companyCategoriesData.slice(i, i + maxBatchSize);
          await database.write(async () => {
            const batch = chunk.map(companyCategoryData =>
              this.collection.prepareCreate(companyCategory => {
                companyCategory.apiId = companyCategoryData.apiId;
                companyCategory.companyId = companyCategoryData.companyId;
                companyCategory.categoryId = companyCategoryData.categoryId;
              })
            );
            await database.batch(batch);
          });
        }
      }

      console.log(`Batch created ${companyCategoriesData.length} company categories`);
    } catch (error) {
      console.error('Error batch creating company categories:', error);
      throw error;
    }
  }

  async batchCreateOrUpdate(companyCategoriesData: CompanyCategoryData[]): Promise<void> {
    try {
      console.log(`[CompanyCategoryRepository] Starting batch create or update for ${companyCategoriesData.length} company categories`);

      // Get all existing company categories by API ID for efficient lookup
      const existingCompanyCategories = await this.collection.query().fetch();
      const existingByApiId = new Map<number, CompanyCategoryData>();
      
      existingCompanyCategories.forEach(companyCategory => {
        existingByApiId.set(companyCategory.apiId, {
          id: companyCategory.id,
          apiId: companyCategory.apiId,
          companyId: companyCategory.companyId,
          categoryId: companyCategory.categoryId,
          created_at: companyCategory.createdAt,
          updated_at: companyCategory.updatedAt,
        });
      });

      console.log(`[CompanyCategoryRepository] Found ${existingByApiId.size} existing company categories`);

      // Process in chunks to avoid memory issues
      const chunkSize = 250;
      for (let i = 0; i < companyCategoriesData.length; i += chunkSize) {
        const chunk = companyCategoriesData.slice(i, i + chunkSize);
        
        await database.write(async () => {
          const batch: any[] = [];

          chunk.forEach(companyCategoryData => {
            if (!companyCategoryData.apiId) {
              console.warn('[CompanyCategoryRepository] Missing apiId for company category:', companyCategoryData);
              return;
            }

            const existing = existingByApiId.get(companyCategoryData.apiId);

            if (existing && existing.id) {
              // Update existing company category
              const companyCategoryToUpdate = this.collection.find(existing.id);
              batch.push(
                companyCategoryToUpdate.then(cc =>
                  cc.prepareUpdate(companyCategory => {
                    companyCategory.apiId = companyCategoryData.apiId;
                    companyCategory.companyId = companyCategoryData.companyId;
                    companyCategory.categoryId = companyCategoryData.categoryId;
                  })
                )
              );
            } else {
              // Create new company category
              batch.push(
                this.collection.prepareCreate(companyCategory => {
                  companyCategory.apiId = companyCategoryData.apiId;
                  companyCategory.companyId = companyCategoryData.companyId;
                  companyCategory.categoryId = companyCategoryData.categoryId;
                })
              );
            }
          });

          // Resolve all promises and execute batch
          const resolvedBatch = await Promise.all(batch);
          await database.batch(resolvedBatch);
        });

        console.log(`[CompanyCategoryRepository] Processed ${i + chunk.length}/${companyCategoriesData.length} company categories`);
      }

      console.log(`[CompanyCategoryRepository] Successfully processed ${companyCategoriesData.length} company categories`);
    } catch (error) {
      console.error('Error batch creating or updating company categories:', error);
      throw error;
    }
  }

  async clearAll(): Promise<void> {
    try {
      await database.write(async () => {
        const allCompanyCategories = await this.collection.query().fetch();
        const batch = allCompanyCategories.map(companyCategory => companyCategory.prepareDestroyPermanently());
        await database.batch(batch);
      });
      console.log('Cleared all company categories from database');
    } catch (error) {
      console.error('Error clearing company categories:', error);
      throw error;
    }
  }
}

export default new WatermelonCompanyCategoryRepository();
