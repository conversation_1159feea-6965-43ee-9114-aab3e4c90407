import { Q } from '@nozbe/watermelondb';
import database from '../database';
import SyncState from '../models/SyncState';

export interface SyncStateData {
  id?: string;
  syncKey: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  progress: number;
  lastSyncAt?: Date;
  lastDataTime?: number; // Epoch timestamp for lastDateTime API parameter
  errorMessage?: string;
  retryCount: number;
  createdAt?: Date;
  updatedAt?: Date;
}

class WatermelonSyncStateRepository {
  private collection = database.get<SyncState>('sync_state');

  async getBySyncKey(syncKey: string): Promise<SyncStateData | null> {
    try {
      const syncStates = await this.collection
        .query(Q.where('sync_key', syncKey))
        .fetch();

      if (syncStates.length === 0) {
        return null;
      }

      const syncState = syncStates[0];
      return {
        id: syncState.id,
        syncKey: syncState.syncKey,
        status: syncState.status as any,
        progress: syncState.progress,
        lastSyncAt: syncState.lastSyncAt,
        lastDataTime: syncState.lastDataTime,
        errorMessage: syncState.errorMessage,
        retryCount: syncState.retryCount,
        createdAt: syncState.createdAt,
        updatedAt: syncState.updatedAt,
      };
    } catch (error) {
      console.error('Error getting sync state by key:', error);
      return null;
    }
  }

  async create(syncStateData: Omit<SyncStateData, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const newSyncState = await database.write(async () => {
        return await this.collection.create(syncState => {
          syncState.syncKey = syncStateData.syncKey;
          syncState.status = syncStateData.status;
          syncState.progress = syncStateData.progress;
          syncState.lastSyncAt = syncStateData.lastSyncAt;
          syncState.lastDataTime = syncStateData.lastDataTime;
          syncState.errorMessage = syncStateData.errorMessage;
          syncState.retryCount = syncStateData.retryCount;
        });
      });
      return newSyncState.id;
    } catch (error) {
      console.error('Error creating sync state:', error);
      throw error;
    }
  }

  async update(syncStateData: SyncStateData): Promise<void> {
    if (!syncStateData.id) {
      throw new Error('Sync state ID is required for update');
    }

    try {
      await database.write(async () => {
        const syncState = await this.collection.find(syncStateData.id!);
        await syncState.update(syncState => {
          syncState.status = syncStateData.status;
          syncState.progress = syncStateData.progress;
          syncState.lastSyncAt = syncStateData.lastSyncAt;
          syncState.lastDataTime = syncStateData.lastDataTime;
          syncState.errorMessage = syncStateData.errorMessage;
          syncState.retryCount = syncStateData.retryCount;
        });
      });
    } catch (error) {
      console.error('Error updating sync state:', error);
      throw error;
    }
  }

  async createOrUpdate(syncStateData: Omit<SyncStateData, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const existing = await this.getBySyncKey(syncStateData.syncKey);
    if (existing) {
      await this.update({ ...syncStateData, id: existing.id });
      return existing.id;
    } else {
      return await this.create(syncStateData);
    }
  }

  async getPendingOrFailedSyncs(): Promise<SyncStateData[]> {
    try {
      const syncStates = await this.collection
        .query(
          Q.or(
            Q.where('status', 'pending'),
            Q.where('status', 'failed')
          )
        )
        .fetch();

      return syncStates.map(syncState => ({
        id: syncState.id,
        syncKey: syncState.syncKey,
        status: syncState.status as any,
        progress: syncState.progress,
        lastSyncAt: syncState.lastSyncAt,
        lastDataTime: syncState.lastDataTime,
        errorMessage: syncState.errorMessage,
        retryCount: syncState.retryCount,
        createdAt: syncState.createdAt,
        updatedAt: syncState.updatedAt,
      }));
    } catch (error) {
      console.error('Error getting pending/failed syncs:', error);
      return [];
    }
  }

  async delete(id: string): Promise<void> {
    try {
      await database.write(async () => {
        const syncState = await this.collection.find(id);
        await syncState.destroyPermanently();
      });
    } catch (error) {
      console.error('Error deleting sync state:', error);
      throw error;
    }
  }
}

export default new WatermelonSyncStateRepository();
