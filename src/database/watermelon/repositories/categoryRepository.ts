import { Q } from '@nozbe/watermelondb';
import database from '../database';
import Category from '../models/Category';

export interface CategoryData {
  categoryId?: string; // WatermelonDB ID (string)
  apiCategoryId?: number; // Original API categoryId (number)
  name: string;
  iconUrl?: string;
  isActive?: number;
  categoryPriority?: number; // Priority for sorting (lower values appear first)
  created_at?: Date;
  updated_at?: Date;
}

class WatermelonCategoryRepository {
  private collection = database.get<Category>('categories');

  async getAll(): Promise<CategoryData[]> {
    try {
      const categories = await this.collection.query().fetch();
      return categories.map(category => ({
        categoryId: category.id,
        apiCategoryId: category.apiCategoryId,
        name: category.name,
        iconUrl: category.iconUrl,
        isActive: category.isActive,
        categoryPriority: category.categoryPriority,
        created_at: category.createdAt,
        updated_at: category.updatedAt,
      }));
    } catch (error) {
      console.error('Error getting categories:', error);
      throw error;
    }
  }

  async getById(id: string): Promise<CategoryData | null> {
    try {
      const category = await this.collection.find(id);
      return {
        categoryId: category.id,
        apiCategoryId: category.apiCategoryId,
        name: category.name,
        iconUrl: category.iconUrl,
        isActive: category.isActive,
        created_at: category.createdAt,
        updated_at: category.updatedAt,
      };
    } catch (error) {
      console.error('Error getting category by id:', error);
      return null;
    }
  }

  async getByApiCategoryId(apiCategoryId: number): Promise<CategoryData | null> {
    try {
      const categories = await this.collection
        .query(Q.where('api_category_id', apiCategoryId))
        .fetch();

      if (categories.length === 0) {
        return null;
      }

      const category = categories[0];
      return {
        categoryId: category.id,
        apiCategoryId: category.apiCategoryId,
        name: category.name,
        iconUrl: category.iconUrl,
        isActive: category.isActive,
        categoryPriority: category.categoryPriority,
        created_at: category.createdAt,
        updated_at: category.updatedAt,
      };
    } catch (error) {
      console.error('Error getting category by API category id:', error);
      return null;
    }
  }

  async create(categoryData: CategoryData): Promise<string> {
    try {
      const newCategory = await database.write(async () => {
        return await this.collection.create(category => {
          category.apiCategoryId = categoryData.apiCategoryId || 0;
          category.name = categoryData.name;
          category.iconUrl = categoryData.iconUrl || '';
          category.isActive = categoryData.isActive || 1;
          category.categoryPriority = categoryData.categoryPriority || 999; // Default to high value if not provided
        });
      });
      return newCategory.id;
    } catch (error) {
      console.error('Error creating category:', error);
      throw error;
    }
  }

  async update(categoryData: CategoryData): Promise<boolean> {
    if (!categoryData.categoryId) {
      throw new Error('Category ID is required for update');
    }

    try {
      await database.write(async () => {
        const category = await this.collection.find(categoryData.categoryId!);
        await category.update(category => {
          category.apiCategoryId = categoryData.apiCategoryId || 0;
          category.name = categoryData.name;
          category.iconUrl = categoryData.iconUrl || '';
          category.isActive = categoryData.isActive || 1;
          category.categoryPriority = categoryData.categoryPriority || 999; // Default to high value if not provided
        });
      });
      return true;
    } catch (error) {
      console.error('Error updating category:', error);
      throw error;
    }
  }

  async delete(id: string): Promise<boolean> {
    try {
      await database.write(async () => {
        const category = await this.collection.find(id);
        await category.markAsDeleted();
      });
      return true;
    } catch (error) {
      console.error('Error deleting category:', error);
      throw error;
    }
  }

  async getActiveCategories(): Promise<CategoryData[]> {
    try {
      const categories = await this.collection
        .query(Q.where('is_active', 1))
        .fetch();

      return categories.map(category => ({
        categoryId: category.id,
        apiCategoryId: category.apiCategoryId,
        name: category.name,
        iconUrl: category.iconUrl,
        isActive: category.isActive,
        categoryPriority: category.categoryPriority,
        created_at: category.createdAt,
        updated_at: category.updatedAt,
      }));
    } catch (error) {
      console.error('Error getting active categories:', error);
      throw error;
    }
  }

  async getCount(): Promise<number> {
    try {
      const count = await this.collection.query().fetchCount();
      return count;
    } catch (error) {
      console.error('Error getting categories count:', error);
      throw error;
    }
  }

  async createOrUpdate(categoryData: CategoryData): Promise<string> {
    try {
      // First check if category exists by apiCategoryId
      if (categoryData.apiCategoryId) {
        const existing = await this.getByApiCategoryId(categoryData.apiCategoryId);
        if (existing && existing.categoryId) {
          // Update existing category
          await this.update({
            ...categoryData,
            categoryId: existing.categoryId,
          });
          return existing.categoryId;
        }
      }

      // Create new category if not found
      return await this.create(categoryData);
    } catch (error) {
      console.error('Error creating or updating category:', error);
      throw error;
    }
  }

  async clearAll(): Promise<void> {
    try {
      await database.write(async () => {
        const allCategories = await this.collection.query().fetch();
        console.log(`[CategoryRepository] Clearing ${allCategories.length} existing categories`);

        // Log some existing categories for debugging
        if (allCategories.length > 0) {
          console.log('[CategoryRepository] Sample existing category:', {
            id: allCategories[0].id,
            name: allCategories[0].name,
            apiCategoryId: allCategories[0].apiCategoryId,
          });
        }

        await Promise.all(allCategories.map(category => category.destroyPermanently()));
      });
      console.log('[CategoryRepository] Successfully cleared all categories');
    } catch (error) {
      console.error('Error clearing all categories:', error);
      throw error;
    }
  }

  async forceReset(): Promise<void> {
    try {
      console.log('[CategoryRepository] Force resetting all categories...');
      await database.write(async () => {
        const allCategories = await this.collection.query().fetch();
        console.log(`[CategoryRepository] Force deleting ${allCategories.length} categories`);
        await Promise.all(allCategories.map(category => category.destroyPermanently()));
      });
      console.log('[CategoryRepository] Force reset completed');
    } catch (error) {
      console.error('Error force resetting categories:', error);
      throw error;
    }
  }

  async batchCreate(categoriesData: CategoryData[]): Promise<void> {
    try {
      console.log(`[CategoryRepository] Batch creating ${categoriesData.length} categories`);

      // Log first category for debugging
      if (categoriesData.length > 0) {
        console.log('[CategoryRepository] Sample category data:', JSON.stringify(categoriesData[0], null, 2));
      }

      await database.write(async () => {
        const batch = categoriesData.map(categoryData =>
          this.collection.prepareCreate(category => {
            if (!categoryData.apiCategoryId) {
              console.warn('[CategoryRepository] Missing apiCategoryId for category:', categoryData.name);
            }
            category.apiCategoryId = categoryData.apiCategoryId || 0;
            category.name = categoryData.name;
            category.iconUrl = categoryData.iconUrl || '';
            category.isActive = categoryData.isActive || 1;
            category.categoryPriority = categoryData.categoryPriority || 999; // Default to high value if not provided
          })
        );
        await database.batch(batch);
      });

      console.log(`[CategoryRepository] Successfully created ${categoriesData.length} categories`);
    } catch (error) {
      console.error('Error batch creating categories:', error);
      throw error;
    }
  }

  async batchCreateOrUpdate(categoriesData: CategoryData[]): Promise<void> {
    try {
      console.log(`[CategoryRepository] Batch creating/updating ${categoriesData.length} categories`);

      // Log first category for debugging
      if (categoriesData.length > 0) {
        console.log('[CategoryRepository] Sample category data:', JSON.stringify(categoriesData[0], null, 2));
      }

      // Get all existing categories by apiCategoryId for efficient lookup
      const existingCategories = await this.getAll();
      const existingByApiId = new Map<number, CategoryData>();
      existingCategories.forEach(cat => {
        if (cat.apiCategoryId) {
          existingByApiId.set(cat.apiCategoryId, cat);
        }
      });

      console.log(`[CategoryRepository] Found ${existingByApiId.size} existing categories for comparison`);

      await database.write(async () => {
        const batch: any[] = [];

        categoriesData.forEach(categoryData => {
          if (!categoryData.apiCategoryId) {
            console.warn('[CategoryRepository] Missing apiCategoryId for category:', categoryData.name);
            return;
          }

          const existing = existingByApiId.get(categoryData.apiCategoryId);

          if (existing && existing.categoryId) {
            // Update existing category
            const categoryToUpdate = this.collection.find(existing.categoryId);
            batch.push(
              categoryToUpdate.then(cat =>
                cat.prepareUpdate(category => {
                  category.apiCategoryId = categoryData.apiCategoryId || 0;
                  category.name = categoryData.name;
                  category.iconUrl = categoryData.iconUrl || '';
                  category.isActive = categoryData.isActive || 1;
                  category.categoryPriority = categoryData.categoryPriority || 999; // Default to high value if not provided
                })
              )
            );
          } else {
            // Create new category
            batch.push(
              this.collection.prepareCreate(category => {
                category.apiCategoryId = categoryData.apiCategoryId || 0;
                category.name = categoryData.name;
                category.iconUrl = categoryData.iconUrl || '';
                category.isActive = categoryData.isActive || 1;
                category.categoryPriority = categoryData.categoryPriority || 999; // Default to high value if not provided
              })
            );
          }
        });

        // Resolve all promises and execute batch
        const resolvedBatch = await Promise.all(batch);
        await database.batch(resolvedBatch);
      });

      console.log(`[CategoryRepository] Successfully batch created/updated ${categoriesData.length} categories`);
    } catch (error) {
      console.error('Error batch creating/updating categories:', error);
      throw error;
    }
  }
}

export default new WatermelonCategoryRepository();
