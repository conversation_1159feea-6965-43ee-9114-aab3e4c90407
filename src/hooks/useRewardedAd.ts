import { useCallback } from 'react';
import adMobService from '../services/adMobService';

interface UseRewardedAdReturn {
  showRewardedAd: () => Promise<boolean>;
  isAdMobReady: boolean;
}

/**
 * Hook for showing rewarded ads
 */
export const useRewardedAd = (): UseRewardedAdReturn => {
  const showRewardedAd = useCallback(async (): Promise<boolean> => {
    try {
      if (!adMobService.isAdMobInitialized()) {
        console.log('[RewardedAd] AdMob not initialized yet');
        return false;
      }

      const rewarded = await adMobService.showRewardedAd();
      return rewarded;
    } catch (error) {
      console.error('[RewardedAd] Error showing rewarded ad:', error);
      return false;
    }
  }, []);

  return {
    showRewardedAd,
    isAdMobReady: adMobService.isAdMobInitialized(),
  };
};
