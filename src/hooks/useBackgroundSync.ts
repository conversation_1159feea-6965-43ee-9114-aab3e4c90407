import { useState, useEffect } from 'react';
import backgroundSyncManager, { SyncKey } from '../services/backgroundSyncManager';
import { SyncStateData } from '../database/watermelon/repositories/syncStateRepository';

interface SyncStatus {
  isLoading: boolean;
  progress: number;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  error?: string;
  lastSyncAt?: Date;
}

export const useBackgroundSync = (syncKey: SyncKey) => {
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    isLoading: false,
    progress: 0,
    status: 'pending',
  });

  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    const updateSyncStatus = async () => {
      try {
        const syncState = await backgroundSyncManager.getSyncStatus(syncKey);

        if (syncState) {
          setSyncStatus({
            isLoading: syncState.status === 'in_progress',
            progress: syncState.progress,
            status: syncState.status,
            error: syncState.errorMessage,
            lastSyncAt: syncState.lastSyncAt,
          });
        }
      } catch (error) {
        console.error(`Error getting sync status for ${syncKey}:`, error);
      }
    };

    // Initial check
    updateSyncStatus();

    // Poll for updates every 2 seconds when sync is in progress
    intervalId = setInterval(updateSyncStatus, 2000);

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [syncKey]);

  const resetSync = async () => {
    try {
      await backgroundSyncManager.resetSync(syncKey);
      setSyncStatus(prev => ({
        ...prev,
        status: 'pending',
        progress: 0,
        error: undefined,
      }));
    } catch (error) {
      console.error(`Error resetting sync for ${syncKey}:`, error);
    }
  };

  return {
    ...syncStatus,
    resetSync,
  };
};

export const useAllSyncStatus = () => {
  const categoriesSync = useBackgroundSync('categories');
  const companiesSync = useBackgroundSync('companies');
  const companyCategoriesSync = useBackgroundSync('company_categories');

  const isAnySyncInProgress = categoriesSync.isLoading || companiesSync.isLoading || companyCategoriesSync.isLoading;
  const allSyncsCompleted = categoriesSync.status === 'completed' && companiesSync.status === 'completed' && companyCategoriesSync.status === 'completed';
  const anySyncFailed = categoriesSync.status === 'failed' || companiesSync.status === 'failed' || companyCategoriesSync.status === 'failed';

  const overallProgress = Math.round((categoriesSync.progress + companiesSync.progress + companyCategoriesSync.progress) / 3);

  return {
    categories: categoriesSync,
    companies: companiesSync,
    companyCategories: companyCategoriesSync,
    isAnySyncInProgress,
    allSyncsCompleted,
    anySyncFailed,
    overallProgress,
  };
};
