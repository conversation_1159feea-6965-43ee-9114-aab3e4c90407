import { useState, useEffect } from 'react';
import {
  getBankHolidayStatusForCategory,
} from '../utils/bankHolidayUtils';



/**
 * Hook for managing bank holiday state for categories
 * @param categoryId - Category ID to check
 * @param date - Optional date to check (defaults to today)
 * @returns Object with bank holiday state and message
 */
export const useBankHolidayForCategory = (
  categoryId: number,
  date?: Date,
) => {
  const [isBankCategory, setIsBankCategory] = useState(false);
  const [bankHolidayMessage, setBankHolidayMessage] = useState('');

  useEffect(() => {
    const status = getBankHolidayStatusForCategory(categoryId, date);
    
    setIsBankCategory(status.isHoliday);
    setBankHolidayMessage(status.message);

    // Debug logging
    const dateStr = date ? date.toDateString() : 'today';
    console.log(
      `[BankHoliday] Category ${categoryId} - Bank category: ${status.isBankCategory}, Holiday: ${status.isHoliday}, Date: ${dateStr}`,
    );

    if (status.isHoliday) {
      console.log(`[BankHoliday] Message: ${status.message}`);
    }
  }, [categoryId, date]);

  return {
    isBankCategory,
    bankHolidayMessage,
  };
};
