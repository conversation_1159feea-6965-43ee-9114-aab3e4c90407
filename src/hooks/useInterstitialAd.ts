import { useCallback } from 'react';
import adMobService from '../services/adMobService';

interface UseInterstitialAdReturn {
  showInterstitialAd: () => Promise<void>;
  isAdMobReady: boolean;
}

/**
 * Hook for showing interstitial ads
 */
export const useInterstitialAd = (): UseInterstitialAdReturn => {
  const showInterstitialAd = useCallback(async () => {
    try {
      if (!adMobService.isAdMobInitialized()) {
        console.log('[InterstitialAd] AdMob not initialized yet');
        return;
      }

      await adMobService.showInterstitialAd();
    } catch (error) {
      console.error('[InterstitialAd] Error showing interstitial ad:', error);
    }
  }, []);

  return {
    showInterstitialAd,
    isAdMobReady: adMobService.isAdMobInitialized(),
  };
};
