import { useState, useEffect } from 'react';
import appStateService from '../services/appStateService';

interface FirstLaunchState {
  isFirstLaunch: boolean | null; // null means loading
  isLoading: boolean;
  error: string | null;
  markCompleted: () => Promise<void>;
  reset: () => Promise<void>;
}

export const useFirstLaunch = (): FirstLaunchState => {
  const [isFirstLaunch, setIsFirstLaunch] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Check first launch status on mount
  useEffect(() => {
    checkFirstLaunchStatus();
  }, []);

  const checkFirstLaunchStatus = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const firstLaunch = await appStateService.isFirstLaunch();
      setIsFirstLaunch(firstLaunch);
      
      console.log(`[useFirstLaunch] First launch status: ${firstLaunch}`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('[useFirstLaunch] Error checking first launch:', err);
      
      // Default to first launch in case of error
      setIsFirstLaunch(true);
    } finally {
      setIsLoading(false);
    }
  };

  const markCompleted = async () => {
    try {
      await appStateService.markFirstLaunchCompleted();
      setIsFirstLaunch(false);
      console.log('[useFirstLaunch] First launch marked as completed');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('[useFirstLaunch] Error marking first launch completed:', err);
      throw err;
    }
  };

  const reset = async () => {
    try {
      await appStateService.resetFirstLaunchFlag();
      setIsFirstLaunch(true);
      console.log('[useFirstLaunch] First launch flag reset');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('[useFirstLaunch] Error resetting first launch flag:', err);
      throw err;
    }
  };

  return {
    isFirstLaunch,
    isLoading,
    error,
    markCompleted,
    reset,
  };
};
