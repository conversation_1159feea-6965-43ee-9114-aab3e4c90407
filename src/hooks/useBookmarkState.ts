import {useState, useEffect, useCallback} from 'react';
import bookmarkStorageService from '../services/bookmarkStorageService';

interface UseBookmarkStateReturn {
  isBookmarked: boolean;
  hasShortcut: boolean;
  isLoading: boolean;
  toggleBookmark: () => Promise<void>;
  refreshState: () => Promise<void>;
}

export const useBookmarkState = (
  companyId: number | undefined,
  companyName: string | undefined,
): UseBookmarkStateReturn => {
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [hasShortcut, setHasShortcut] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Load initial state
  const loadBookmarkState = useCallback(async () => {
    if (!companyId) return;

    try {
      const [bookmarked, shortcut] = await Promise.all([
        bookmarkStorageService.isCompanyBookmarked(companyId),
        bookmarkStorageService.hasShortcut(companyId),
      ]);

      setIsBookmarked(bookmarked);
      setHasShortcut(shortcut);
    } catch (error) {
      console.error('[useBookmarkState] Error loading bookmark state:', error);
    }
  }, [companyId]);

  // Refresh state (can be called externally)
  const refreshState = useCallback(async () => {
    await loadBookmarkState();
  }, [loadBookmarkState]);

  // Toggle bookmark state
  const toggleBookmark = useCallback(async () => {
    if (!companyId || !companyName) {
      console.warn('[useBookmarkState] Missing companyId or companyName');
      return;
    }

    setIsLoading(true);
    try {
      if (isBookmarked) {
        // Remove bookmark
        await bookmarkStorageService.removeBookmark(companyId);
        setIsBookmarked(false);
        
        // If it had a shortcut, also remove the shortcut record
        if (hasShortcut) {
          await bookmarkStorageService.removeShortcut(companyId);
          setHasShortcut(false);
        }
      } else {
        // Add bookmark (without shortcut initially)
        await bookmarkStorageService.addBookmark(companyId, companyName, false);
        setIsBookmarked(true);
      }
    } catch (error) {
      console.error('[useBookmarkState] Error toggling bookmark:', error);
      // Revert state on error
      await loadBookmarkState();
    } finally {
      setIsLoading(false);
    }
  }, [companyId, companyName, isBookmarked, hasShortcut, loadBookmarkState]);

  // Load state on mount and when companyId changes
  useEffect(() => {
    loadBookmarkState();
  }, [loadBookmarkState]);

  return {
    isBookmarked,
    hasShortcut,
    isLoading,
    toggleBookmark,
    refreshState,
  };
};
