// React Native WebSocket Service for Vote Functionality
class WebSocketVoteService {
  constructor() {
    this.websocket = null;
    this.isConnecting = false;
    this.messageQueue = [];
    this.pendingVotes = new Map(); // Track pending votes by nid
  }

  // Initialize WebSocket connection with timeout
  connect() {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      console.log('WebSocket already connected');
      return Promise.resolve();
    }

    if (this.isConnecting) {
      console.log('WebSocket connection already in progress, waiting...');
      return new Promise((resolve, reject) => {
        const checkConnection = () => {
          if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            resolve();
          } else if (!this.isConnecting) {
            reject(new Error('Connection attempt failed'));
          } else {
            setTimeout(checkConnection, 100);
          }
        };
        checkConnection();
      });
    }

    this.isConnecting = true;
    console.log(
      'Starting WebSocket connection to wss://updateadhaar.com:31040',
    );

    return new Promise((resolve, reject) => {
      // Set connection timeout
      const connectionTimeout = setTimeout(() => {
        console.log('WebSocket connection timeout');
        this.isConnecting = false;
        if (this.websocket) {
          this.websocket.close();
          this.websocket = null;
        }
        reject(new Error('WebSocket connection timeout'));
      }, 10000); // 10 second timeout

      try {
        this.websocket = new WebSocket('wss://updateadhaar.com:31040');

        this.websocket.onopen = () => {
          console.log('✅ WebSocket connected successfully for voting');
          clearTimeout(connectionTimeout);
          this.isConnecting = false;

          // Process any queued messages
          while (this.messageQueue.length > 0) {
            const queuedData = this.messageQueue.shift();
            this.sendVoteData(queuedData);
          }

          resolve();
        };

        this.websocket.onclose = event => {
          console.log('WebSocket connection closed:', event.code, event.reason);
          clearTimeout(connectionTimeout);
          this.websocket = null;
          this.isConnecting = false;

          // Don't treat expected closures as errors
          if (event.code === 1006 && this.pendingVotes.size === 0) {
            console.log(
              'ℹ️ Connection closed after successful operations (expected behavior)',
            );
          }
        };

        this.websocket.onmessage = evt => {
          console.log('Vote response from server:', evt.data);
          try {
            // Try to parse as JSON first
            const response = JSON.parse(evt.data);
            console.log('Parsed vote response:', response);
            this.handleVoteResponse(response);
          } catch (ex) {
            console.log(
              'Response is not JSON, treating as plain text:',
              evt.data,
            );
            // Handle plain text responses (like 'success')
            this.handlePlainTextResponse(evt.data);
          }
        };

        this.websocket.onerror = evt => {
          // Only log as error if we're still connecting or have pending operations
          if (this.isConnecting || this.pendingVotes.size > 0) {
            console.log('❌ WebSocket error during active operation:', evt);
            clearTimeout(connectionTimeout);
            this.isConnecting = false;
            this.websocket = null;
            reject(new Error('WebSocket connection failed'));
          } else {
            // This is likely a post-success cleanup error, log as info
            console.log(
              'ℹ️ WebSocket error after operations completed (likely cleanup):',
              {
                type: evt.type || 'unknown',
                readyState: evt.target ? evt.target.readyState : 'unknown',
                isTrusted: evt.isTrusted,
                timeStamp: evt.timeStamp,
              },
            );
          }
        };
      } catch (error) {
        console.log('❌ Error creating WebSocket:', error);
        clearTimeout(connectionTimeout);
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  // Send vote data to server
  async sendVote(phoneNumber, isUpvote) {
    console.log(
      `📤 Attempting to send ${
        isUpvote ? 'upvote' : 'downvote'
      } for phone: ${phoneNumber}`,
    );

    const nid = Date.now(); // Use timestamp as unique id
    const voteData = {
      op: 'saveupdown',
      website: 'indiacustomercare.com',
      nid: nid,
      phone: phoneNumber,
      data: {
        up: isUpvote ? 1 : 0,
        down: isUpvote ? 0 : -1,
      },
      l: 'valid-license', // license key
      src: 'iccapp',
      created: Date.now(),
      checksum: Math.floor(Math.random() * 100000000), // Random checksum for now
    };

    return new Promise(async (resolve, reject) => {
      try {
        // Check if already connected, if not try to connect
        if (!this.isConnected()) {
          console.log('🔌 WebSocket not connected, attempting to connect...');
          const connected = await this.initializeConnection(0, 2); // Try up to 3 times

          if (!connected) {
            console.log(
              '❌ Failed to establish WebSocket connection for voting',
            );
            // Still allow local vote counting even if WebSocket fails
            console.log(
              '📋 Allowing local vote counting despite connection failure',
            );
            resolve({
              success: true,
              timeout: true,
              connectionFailed: true,
              phoneNumber,
              isUpvote,
            });
            return;
          }
        }

        console.log('✅ WebSocket connected, sending vote data...');

        // Store the promise resolvers for this vote
        this.pendingVotes.set(nid, {resolve, reject, phoneNumber, isUpvote});

        // Set timeout for vote response
        setTimeout(() => {
          if (this.pendingVotes.has(nid)) {
            this.pendingVotes.delete(nid);
            console.log('⏰ Vote response timeout for nid:', nid);
            resolve({success: true, timeout: true, phoneNumber, isUpvote}); // Still consider it successful but with timeout
          }
        }, 10000); // 10 second timeout

        this.sendVoteData(voteData);
      } catch (error) {
        console.log('❌ Failed to connect WebSocket:', error.message);
        // Queue the message for later sending
        this.messageQueue.push(voteData);
        console.log('📋 Vote queued for later sending');

        // Allow local vote counting even if WebSocket fails
        console.log('📋 Allowing local vote counting despite WebSocket error');
        resolve({
          success: true,
          timeout: true,
          error: error.message,
          phoneNumber,
          isUpvote,
        });
      }
    });
  }

  // Internal method to send data through WebSocket
  sendVoteData(voteData) {
    if (!this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
      console.log('⚠️ WebSocket not ready, queuing message');
      this.messageQueue.push(voteData);
      return;
    }

    // Add required fields
    voteData.source = 128; // Client identifier
    voteData.dataversion = 'NotEncrypted-v1.0';
    voteData.version = 'snd1.0';
    voteData.new_html_version = '1';

    const payload = JSON.stringify(voteData);
    console.log('📤 Sending vote payload:', payload);

    // Send immediately if connection is ready
    if (this.websocket.readyState === WebSocket.OPEN) {
      try {
        this.websocket.send(payload);
        console.log('✅ Vote message sent successfully!');
      } catch (error) {
        console.log('❌ Failed to send vote message:', error);
        this.messageQueue.push(voteData);
      }
    } else {
      // Wait for connection if not ready
      this.waitForSocketConnection(this.websocket, () => {
        try {
          this.websocket.send(payload);
          console.log('✅ Vote message sent after waiting!');
        } catch (error) {
          console.log('❌ Failed to send vote message after waiting:', error);
          this.messageQueue.push(voteData);
        }
      });
    }
  }

  // Wait for socket connection helper with timeout
  waitForSocketConnection(socket, callback, attempts = 0, maxAttempts = 100) {
    setTimeout(() => {
      if (socket.readyState === 1) {
        console.log('🔗 Socket connection ready');
        if (callback != null) {
          callback();
        }
        return;
      } else if (attempts >= maxAttempts) {
        console.log(
          '⏰ Socket connection timeout after',
          maxAttempts * 50,
          'ms',
        );
        return;
      } else {
        this.waitForSocketConnection(
          socket,
          callback,
          attempts + 1,
          maxAttempts,
        );
      }
    }, 50);
  }

  // Handle vote response from server
  handleVoteResponse(response) {
    console.log('🔄 Processing vote response:', response);

    // Check if this is a response to one of our pending votes
    if (response && response.nid) {
      const pendingVote = this.pendingVotes.get(response.nid);
      if (pendingVote) {
        console.log('✅ Found matching pending vote for nid:', response.nid);
        this.pendingVotes.delete(response.nid);

        // Check if the response indicates success
        if (
          response.status === 'success' ||
          response.success === true ||
          response.result === 'ok'
        ) {
          console.log('🎉 Vote confirmed successful by server');
          pendingVote.resolve({
            success: true,
            confirmed: true,
            response: response,
            phoneNumber: pendingVote.phoneNumber,
            isUpvote: pendingVote.isUpvote,
          });
        } else {
          console.log('❌ Vote failed according to server response');
          pendingVote.reject(
            new Error(
              `Server rejected vote: ${response.message || 'Unknown error'}`,
            ),
          );
        }
      } else {
        console.log('⚠️ Received response for unknown vote nid:', response.nid);
      }
    } else {
      console.log(
        '⚠️ Received response without nid, treating as general confirmation',
      );
      // If we have any pending votes and no specific nid, resolve the oldest one
      if (this.pendingVotes.size > 0) {
        const [oldestNid, oldestVote] = this.pendingVotes
          .entries()
          .next().value;
        this.pendingVotes.delete(oldestNid);
        console.log('✅ Resolving oldest pending vote due to general response');
        oldestVote.resolve({
          success: true,
          confirmed: true,
          response: response,
          phoneNumber: oldestVote.phoneNumber,
          isUpvote: oldestVote.isUpvote,
        });
      }
    }
  }

  // Handle plain text responses from server (like 'success')
  handlePlainTextResponse(textResponse) {
    console.log('🔄 Processing plain text response:', textResponse);

    // Check if the response indicates success
    const isSuccess =
      textResponse &&
      (textResponse.toLowerCase().includes('success') ||
        textResponse.toLowerCase().includes('ok') ||
        textResponse.toLowerCase().includes('done'));

    if (isSuccess) {
      console.log('🎉 Plain text response indicates success');
      // If we have any pending votes, resolve the oldest one
      if (this.pendingVotes.size > 0) {
        const [oldestNid, oldestVote] = this.pendingVotes
          .entries()
          .next().value;
        this.pendingVotes.delete(oldestNid);
        console.log('✅ Resolving oldest pending vote due to success response');
        oldestVote.resolve({
          success: true,
          confirmed: true,
          response: textResponse,
          phoneNumber: oldestVote.phoneNumber,
          isUpvote: oldestVote.isUpvote,
        });
      }
    } else {
      console.log('❌ Plain text response does not indicate success');
      // If we have any pending votes, reject the oldest one
      if (this.pendingVotes.size > 0) {
        const [oldestNid, oldestVote] = this.pendingVotes
          .entries()
          .next().value;
        this.pendingVotes.delete(oldestNid);
        console.log('❌ Rejecting oldest pending vote due to failure response');
        oldestVote.reject(new Error(`Server response: ${textResponse}`));
      }
    }
  }

  // Initialize connection early (can be called from CompanyDetailsScreen)
  async initializeConnection(retryCount = 0, maxRetries = 3) {
    try {
      console.log(
        `🔌 Initializing WebSocket connection early... (attempt ${
          retryCount + 1
        }/${maxRetries + 1})`,
      );

      // Check if already connected
      if (this.isConnected()) {
        console.log('✅ WebSocket already connected');
        return true;
      }

      await this.connect();
      console.log('✅ WebSocket connection initialized successfully');
      return true;
    } catch (error) {
      console.log(
        `❌ Failed to initialize WebSocket connection (attempt ${
          retryCount + 1
        }):`,
        error.message,
      );

      // Retry with exponential backoff if we haven't exceeded max retries
      if (retryCount < maxRetries) {
        const delay = Math.pow(2, retryCount) * 1000; // 1s, 2s, 4s
        console.log(`⏳ Retrying WebSocket connection in ${delay}ms...`);

        return new Promise(resolve => {
          setTimeout(async () => {
            const result = await this.initializeConnection(
              retryCount + 1,
              maxRetries,
            );
            resolve(result);
          }, delay);
        });
      }

      console.log(
        '❌ Max retry attempts reached, WebSocket initialization failed',
      );
      return false;
    }
  }

  // Check if connection is ready
  isConnected() {
    return this.websocket && this.websocket.readyState === WebSocket.OPEN;
  }

  // Get connection status
  getConnectionStatus() {
    if (!this.websocket) {
      return 'disconnected';
    }

    switch (this.websocket.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting';
      case WebSocket.OPEN:
        return 'connected';
      case WebSocket.CLOSING:
        return 'closing';
      case WebSocket.CLOSED:
        return 'closed';
      default:
        return 'unknown';
    }
  }

  // Close WebSocket connection
  disconnect() {
    console.log('🔌 Disconnecting WebSocket...');
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }
    // Clear pending votes
    this.pendingVotes.clear();
    this.isConnecting = false;
  }
}

// Export singleton instance
export default new WebSocketVoteService();
