/**
 * Test utility to demonstrate and test the weekly sync functionality
 */

import backgroundSyncManager from '../services/backgroundSyncManager';
import watermelonCompanyRepository from '../database/watermelon/repositories/companyRepository';
import watermelonCompanyCategoryRepository from '../database/watermelon/repositories/companyCategoryRepository';
import watermelonCategoryRepository from '../database/watermelon/repositories/categoryRepository';
import syncStateRepository from '../database/watermelon/repositories/syncStateRepository';
import DatabaseResetUtility from './databaseReset';

export class WeeklySyncTester {

  /**
   * Test the weekly sync logic by simulating different scenarios
   */
  static async testWeeklySync() {
    console.log('\n=== Testing Weekly Sync Logic ===\n');

    try {
      // Test 1: Check current state
      console.log('1. Checking current state...');
      const companyCount = await watermelonCompanyRepository.getCount();
      const lastSyncDate = await backgroundSyncManager.getLastCompanySyncDate();

      console.log(`   - Companies in database: ${companyCount}`);
      console.log(`   - Last sync date: ${lastSyncDate ? lastSyncDate.toISOString() : 'Never'}`);

      // Test 2: Check if sync is needed
      console.log('\n2. Testing sync decision logic...');
      const syncState = await syncStateRepository.getBySyncKey('companies');

      if (companyCount === 0) {
        console.log('   ✅ No companies found - sync should be triggered');
      } else if (!lastSyncDate) {
        console.log('   ✅ No previous sync found - sync should be triggered');
      } else {
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

        if (lastSyncDate < oneWeekAgo) {
          console.log('   ✅ More than a week since last sync - sync should be triggered');
        } else {
          const daysSinceSync = Math.floor((Date.now() - lastSyncDate.getTime()) / (1000 * 60 * 60 * 24));
          console.log(`   ⏳ Last sync was ${daysSinceSync} days ago - sync should be skipped`);
        }
      }

      // Test 3: Simulate sync initialization
      console.log('\n3. Testing sync initialization...');
      await backgroundSyncManager.initialize();

      const updatedSyncState = await syncStateRepository.getBySyncKey('companies');
      console.log(`   - Sync status: ${updatedSyncState?.status}`);
      console.log(`   - Sync progress: ${updatedSyncState?.progress}%`);

      console.log('\n=== Weekly Sync Test Completed ===\n');

    } catch (error) {
      console.error('Error testing weekly sync:', error);
    }
  }

  /**
   * Force a company sync for testing purposes
   */
  static async forceSyncForTesting() {
    console.log('\n=== Force Syncing Companies for Testing ===\n');

    try {
      await backgroundSyncManager.forceSyncCompanies();

      const companyCount = await watermelonCompanyRepository.getCount();
      const lastSyncDate = await backgroundSyncManager.getLastCompanySyncDate();

      console.log(`✅ Force sync completed!`);
      console.log(`   - Companies in database: ${companyCount}`);
      console.log(`   - Last sync date: ${lastSyncDate?.toISOString()}`);

    } catch (error) {
      console.error('Error force syncing companies:', error);
    }
  }

  /**
   * Force a categories sync for testing purposes
   */
  static async forceSyncCategoriesForTesting() {
    console.log('\n=== Force Syncing Categories for Testing ===\n');

    try {
      await backgroundSyncManager.forceSyncCategories();

      const categoryCount = await watermelonCategoryRepository.getCount();
      const lastSyncDate = await backgroundSyncManager.getLastCategorySyncDate();

      console.log(`✅ Force sync completed!`);
      console.log(`   - Categories in database: ${categoryCount}`);
      console.log(`   - Last sync date: ${lastSyncDate?.toISOString()}`);

    } catch (error) {
      console.error('Error force syncing categories:', error);
    }
  }

  /**
   * Force a company categories sync for testing purposes
   */
  static async forceSyncCompanyCategoriesForTesting() {
    console.log('\n=== Force Syncing Company Categories for Testing ===\n');

    try {
      await backgroundSyncManager.forceSyncCompanyCategories();

      const companyCategoryCount = await watermelonCompanyCategoryRepository.getCount();
      const lastSyncDate = await backgroundSyncManager.getLastCompanyCategorySyncDate();

      console.log(`✅ Force sync completed!`);
      console.log(`   - Company categories in database: ${companyCategoryCount}`);
      console.log(`   - Last sync date: ${lastSyncDate?.toISOString()}`);

    } catch (error) {
      console.error('Error force syncing company categories:', error);
    }
  }

  /**
   * Clear categories to test initial sync behavior
   */
  static async clearCategoriesForTesting() {
    console.log('\n=== Clearing Categories for Testing ===\n');

    try {
      await watermelonCategoryRepository.clearAll();

      // Reset sync state
      await syncStateRepository.createOrUpdate({
        syncKey: 'categories',
        status: 'pending',
        progress: 0,
        retryCount: 0,
      });

      console.log('✅ Categories cleared and sync state reset');

    } catch (error) {
      console.error('Error clearing categories:', error);
    }
  }

  /**
   * Clear companies to test initial sync behavior
   */
  static async clearCompaniesForTesting() {
    console.log('\n=== Clearing Companies for Testing ===\n');

    try {
      await watermelonCompanyRepository.clearAll();

      // Reset sync state
      await syncStateRepository.createOrUpdate({
        syncKey: 'companies',
        status: 'pending',
        progress: 0,
        retryCount: 0,
      });

      console.log('✅ Companies cleared and sync state reset');

    } catch (error) {
      console.error('Error clearing companies:', error);
    }
  }

  /**
   * Simulate an old sync date for categories testing
   */
  static async simulateOldCategorySyncDate() {
    console.log('\n=== Simulating Old Categories Sync Date ===\n');

    try {
      const twoWeeksAgo = new Date();
      twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14);

      await syncStateRepository.createOrUpdate({
        syncKey: 'categories',
        status: 'completed',
        progress: 100,
        retryCount: 0,
        lastSyncAt: twoWeeksAgo,
      });

      console.log(`✅ Simulated categories sync date set to: ${twoWeeksAgo.toISOString()}`);
      console.log('   Next sync should be triggered due to old date');

    } catch (error) {
      console.error('Error simulating old categories sync date:', error);
    }
  }

  /**
   * Simulate an old sync date for testing
   */
  static async simulateOldSyncDate() {
    console.log('\n=== Simulating Old Sync Date ===\n');

    try {
      const twoWeeksAgo = new Date();
      twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14);

      await syncStateRepository.createOrUpdate({
        syncKey: 'companies',
        status: 'completed',
        progress: 100,
        retryCount: 0,
        lastSyncAt: twoWeeksAgo,
      });

      console.log(`✅ Simulated sync date set to: ${twoWeeksAgo.toISOString()}`);
      console.log('   Next sync should be triggered due to old date');

    } catch (error) {
      console.error('Error simulating old sync date:', error);
    }
  }

  /**
   * Clear company categories to test initial sync behavior
   */
  static async clearCompanyCategoriesForTesting() {
    console.log('\n=== Clearing Company Categories for Testing ===\n');

    try {
      await watermelonCompanyCategoryRepository.clearAll();

      // Reset sync state
      await syncStateRepository.createOrUpdate({
        syncKey: 'company_categories',
        status: 'pending',
        progress: 0,
        retryCount: 0,
      });

      console.log('✅ Company categories cleared and sync state reset');

    } catch (error) {
      console.error('Error clearing company categories:', error);
    }
  }

  /**
   * Simulate an old sync date for company categories testing
   */
  static async simulateOldCompanyCategorySyncDate() {
    console.log('\n=== Simulating Old Company Categories Sync Date ===\n');

    try {
      const twoWeeksAgo = new Date();
      twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14);

      await syncStateRepository.createOrUpdate({
        syncKey: 'company_categories',
        status: 'completed',
        progress: 100,
        retryCount: 0,
        lastSyncAt: twoWeeksAgo,
      });

      console.log(`✅ Simulated company categories sync date set to: ${twoWeeksAgo.toISOString()}`);
      console.log('   Next sync should be triggered due to old date');

    } catch (error) {
      console.error('Error simulating old company categories sync date:', error);
    }
  }

  /**
   * Get detailed sync information
   */
  static async getSyncInfo() {
    console.log('\n=== Current Sync Information ===\n');

    try {
      // Categories info
      const categoryCount = await watermelonCategoryRepository.getCount();
      const categorySyncState = await syncStateRepository.getBySyncKey('categories');

      console.log('=== CATEGORIES ===');
      console.log(`Categories in database: ${categoryCount}`);
      console.log(`Sync status: ${categorySyncState?.status || 'Not found'}`);
      console.log(`Sync progress: ${categorySyncState?.progress || 0}%`);
      console.log(`Last sync: ${categorySyncState?.lastSyncAt?.toISOString() || 'Never'}`);
      console.log(`Retry count: ${categorySyncState?.retryCount || 0}`);

      // Show sample category data
      if (categoryCount > 0) {
        const categories = await watermelonCategoryRepository.getAll();
        const sampleCategory = categories[0];
        console.log(`Sample category: ID=${sampleCategory.categoryId}, ApiID=${sampleCategory.apiCategoryId}, Name=${sampleCategory.name}`);
      }

      if (categorySyncState?.errorMessage) {
        console.log(`Error message: ${categorySyncState.errorMessage}`);
      }

      // Calculate days since last sync
      if (categorySyncState?.lastSyncAt) {
        const daysSinceSync = Math.floor((Date.now() - categorySyncState.lastSyncAt.getTime()) / (1000 * 60 * 60 * 24));
        console.log(`Days since last sync: ${daysSinceSync}`);
        console.log(`Sync needed: ${daysSinceSync >= 7 ? 'Yes' : 'No'}`);
      }

      // Companies info
      const companyCount = await watermelonCompanyRepository.getCount();
      const companySyncState = await syncStateRepository.getBySyncKey('companies');

      console.log('\n=== COMPANIES ===');
      console.log(`Companies in database: ${companyCount}`);
      console.log(`Sync status: ${companySyncState?.status || 'Not found'}`);
      console.log(`Sync progress: ${companySyncState?.progress || 0}%`);
      console.log(`Last sync: ${companySyncState?.lastSyncAt?.toISOString() || 'Never'}`);
      console.log(`Retry count: ${companySyncState?.retryCount || 0}`);

      // Show sample company data to verify companyId and number fields
      if (companyCount > 0) {
        const companies = await watermelonCompanyRepository.getAll();
        const sampleCompany = companies[0];
        console.log(`Sample company: ID=${sampleCompany.id}, CompanyID=${sampleCompany.company_id}, Name=${sampleCompany.company_name}, Number=${sampleCompany.number || 'N/A'}`);
      }

      if (companySyncState?.errorMessage) {
        console.log(`Error message: ${companySyncState.errorMessage}`);
      }

      // Calculate days since last sync
      if (companySyncState?.lastSyncAt) {
        const daysSinceSync = Math.floor((Date.now() - companySyncState.lastSyncAt.getTime()) / (1000 * 60 * 60 * 24));
        console.log(`Days since last sync: ${daysSinceSync}`);
        console.log(`Sync needed: ${daysSinceSync >= 7 ? 'Yes' : 'No'}`);
      }

      // Company Categories info
      const companyCategoryCount = await watermelonCompanyCategoryRepository.getCount();
      const companyCategorySyncState = await syncStateRepository.getBySyncKey('company_categories');

      console.log('\n=== COMPANY CATEGORIES ===');
      console.log(`Company categories in database: ${companyCategoryCount}`);
      console.log(`Sync status: ${companyCategorySyncState?.status || 'Not found'}`);
      console.log(`Sync progress: ${companyCategorySyncState?.progress || 0}%`);
      console.log(`Last sync: ${companyCategorySyncState?.lastSyncAt?.toISOString() || 'Never'}`);
      console.log(`Retry count: ${companyCategorySyncState?.retryCount || 0}`);

      if (companyCategorySyncState?.errorMessage) {
        console.log(`Error message: ${companyCategorySyncState.errorMessage}`);
      }

      // Calculate days since last sync
      if (companyCategorySyncState?.lastSyncAt) {
        const daysSinceSync = Math.floor((Date.now() - companyCategorySyncState.lastSyncAt.getTime()) / (1000 * 60 * 60 * 24));
        console.log(`Days since last sync: ${daysSinceSync}`);
        console.log(`Sync needed: ${daysSinceSync >= 7 ? 'Yes' : 'No'}`);
      }

    } catch (error) {
      console.error('Error getting sync info:', error);
    }
  }

  /**
   * Reset database when schema errors occur
   */
  static async resetDatabaseForSchemaFix() {
    console.log('\n=== Database Reset for Schema Fix ===\n');

    try {
      await DatabaseResetUtility.resetDatabase();
      console.log('✅ Database reset completed successfully');
      console.log('Please restart the app and wait for sync to complete');
    } catch (error) {
      console.error('❌ Error resetting database:', error);
    }
  }

  /**
   * Reset only company data (useful for schema changes)
   */
  static async resetCompanyDataForSchemaFix() {
    console.log('\n=== Company Data Reset for Schema Fix ===\n');

    try {
      await DatabaseResetUtility.resetCompanyData();
      console.log('✅ Company data reset completed successfully');
    } catch (error) {
      console.error('❌ Error resetting company data:', error);
    }
  }

  /**
   * Check database integrity and schema
   */
  static async checkDatabaseIntegrity() {
    console.log('\n=== Database Integrity Check ===\n');

    try {
      await DatabaseResetUtility.checkDatabaseIntegrity();
    } catch (error) {
      console.error('❌ Error checking database integrity:', error);
    }
  }

  /**
   * Get detailed database information
   */
  static async getDatabaseInfo() {
    console.log('\n=== Database Information ===\n');

    try {
      await DatabaseResetUtility.getDatabaseInfo();
    } catch (error) {
      console.error('❌ Error getting database info:', error);
    }
  }

  /**
   * Comprehensive troubleshooting method
   */
  static async troubleshootDatabaseIssues() {
    console.log('\n=== Database Troubleshooting ===\n');

    try {
      // Step 1: Check current state
      console.log('Step 1: Checking current database state...');
      await this.getDatabaseInfo();

      // Step 2: Check integrity
      console.log('Step 2: Checking database integrity...');
      await this.checkDatabaseIntegrity();

      // Step 3: Get sync info
      console.log('Step 3: Checking sync status...');
      await this.getSyncInfo();

      console.log('\n=== Troubleshooting Completed ===\n');
      console.log('If you see schema validation errors above, run:');
      console.log('WeeklySyncTester.resetDatabaseForSchemaFix()');

    } catch (error) {
      console.error('❌ Error during troubleshooting:', error);
      console.log('\n🔧 Recommended action: Reset the database');
      console.log('Run: WeeklySyncTester.resetDatabaseForSchemaFix()');
    }
  }
}

export default WeeklySyncTester;
