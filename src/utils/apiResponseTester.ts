/**
 * Utility to test and compare API response structures
 * This helps verify that both /company and /company/get-all APIs return the same structure
 */

import axios from 'axios';
import CONFIG from '../config/config';

interface ApiTestResult {
  endpoint: string;
  success: boolean;
  sampleCompany?: any;
  numberFieldType?: string;
  numberFieldValue?: any;
  error?: string;
}

export class ApiResponseTester {
  
  /**
   * Test the /company?categoryId=... endpoint
   */
  static async testCompanyByCategoryAPI(categoryId: number = 2): Promise<ApiTestResult> {
    try {
      const url = `${CONFIG.API_URL}/company?categoryId=${categoryId}&page=1&limit=5&sortBy=created_at&sortOrder=DESC`;
      console.log(`[ApiTest] Testing: ${url}`);
      
      const response = await axios.get(url, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        timeout: 30000,
      });

      if (response.data?.success && response.data?.data?.companies?.length > 0) {
        const sampleCompany = response.data.data.companies[0];
        
        return {
          endpoint: '/company?categoryId=...',
          success: true,
          sampleCompany,
          numberFieldType: typeof sampleCompany.number,
          numberFieldValue: sampleCompany.number,
        };
      } else {
        return {
          endpoint: '/company?categoryId=...',
          success: false,
          error: 'No companies found in response',
        };
      }
    } catch (error: any) {
      return {
        endpoint: '/company?categoryId=...',
        success: false,
        error: error.message || 'Request failed',
      };
    }
  }

  /**
   * Test the /company/get-all endpoint
   */
  static async testCompanyGetAllAPI(): Promise<ApiTestResult> {
    try {
      const url = `${CONFIG.API_URL}/company/get-all?page=1&limit=5&sortBy=created_at&sortOrder=DESC`;
      console.log(`[ApiTest] Testing: ${url}`);
      
      const response = await axios.get(url, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        timeout: 30000,
      });

      if (response.data?.success && response.data?.data?.companies?.length > 0) {
        const sampleCompany = response.data.data.companies[0];
        
        return {
          endpoint: '/company/get-all',
          success: true,
          sampleCompany,
          numberFieldType: typeof sampleCompany.number,
          numberFieldValue: sampleCompany.number,
        };
      } else {
        return {
          endpoint: '/company/get-all',
          success: false,
          error: 'No companies found in response',
        };
      }
    } catch (error: any) {
      return {
        endpoint: '/company/get-all',
        success: false,
        error: error.message || 'Request failed',
      };
    }
  }

  /**
   * Compare both API responses and log the differences
   */
  static async compareAPIResponses(): Promise<void> {
    console.log('\n🔍 [ApiTest] Starting API Response Comparison...\n');

    // Test both APIs
    const [categoryResult, getAllResult] = await Promise.all([
      this.testCompanyByCategoryAPI(2), // Test with Bank category
      this.testCompanyGetAllAPI(),
    ]);

    // Log results
    console.log('📊 [ApiTest] API Response Comparison Results:');
    console.log('=' .repeat(60));
    
    // Category API results
    console.log(`\n1️⃣ ${categoryResult.endpoint}`);
    console.log(`   Success: ${categoryResult.success}`);
    if (categoryResult.success) {
      console.log(`   Number Field Type: ${categoryResult.numberFieldType}`);
      console.log(`   Number Field Value:`, categoryResult.numberFieldValue);
      console.log(`   Sample Company:`, JSON.stringify(categoryResult.sampleCompany, null, 2));
    } else {
      console.log(`   Error: ${categoryResult.error}`);
    }

    // Get-All API results
    console.log(`\n2️⃣ ${getAllResult.endpoint}`);
    console.log(`   Success: ${getAllResult.success}`);
    if (getAllResult.success) {
      console.log(`   Number Field Type: ${getAllResult.numberFieldType}`);
      console.log(`   Number Field Value:`, getAllResult.numberFieldValue);
      console.log(`   Sample Company:`, JSON.stringify(getAllResult.sampleCompany, null, 2));
    } else {
      console.log(`   Error: ${getAllResult.error}`);
    }

    // Compare structures
    if (categoryResult.success && getAllResult.success) {
      console.log('\n🔄 [ApiTest] Structure Comparison:');
      
      const categoryKeys = Object.keys(categoryResult.sampleCompany || {}).sort();
      const getAllKeys = Object.keys(getAllResult.sampleCompany || {}).sort();
      
      console.log(`   Category API fields: [${categoryKeys.join(', ')}]`);
      console.log(`   Get-All API fields: [${getAllKeys.join(', ')}]`);
      
      const missingInCategory = getAllKeys.filter(key => !categoryKeys.includes(key));
      const missingInGetAll = categoryKeys.filter(key => !getAllKeys.includes(key));
      
      if (missingInCategory.length > 0) {
        console.log(`   ⚠️  Missing in Category API: [${missingInCategory.join(', ')}]`);
      }
      
      if (missingInGetAll.length > 0) {
        console.log(`   ⚠️  Missing in Get-All API: [${missingInGetAll.join(', ')}]`);
      }
      
      // Compare number field specifically
      if (categoryResult.numberFieldType !== getAllResult.numberFieldType) {
        console.log(`   🚨 Number field type mismatch!`);
        console.log(`      Category API: ${categoryResult.numberFieldType}`);
        console.log(`      Get-All API: ${getAllResult.numberFieldType}`);
      } else {
        console.log(`   ✅ Number field types match: ${categoryResult.numberFieldType}`);
      }
      
      if (categoryKeys.length === getAllKeys.length && missingInCategory.length === 0 && missingInGetAll.length === 0) {
        console.log(`   ✅ API structures are identical`);
      } else {
        console.log(`   ⚠️  API structures differ`);
      }
    }

    console.log('\n' + '=' .repeat(60));
    console.log('🏁 [ApiTest] API Response Comparison Complete\n');
  }

  /**
   * Test a specific company ID to see the detailed response
   */
  static async testSpecificCompany(companyId: number): Promise<void> {
    try {
      const url = `${CONFIG.API_URL}/company/${companyId}`;
      console.log(`\n🔍 [ApiTest] Testing specific company: ${url}`);
      
      const response = await axios.get(url, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        timeout: 30000,
      });

      console.log(`📊 [ApiTest] Company ${companyId} Response:`);
      console.log(JSON.stringify(response.data, null, 2));
      
      if (response.data?.number) {
        console.log(`\n📞 [ApiTest] Number field analysis:`);
        console.log(`   Type: ${typeof response.data.number}`);
        console.log(`   Value:`, response.data.number);
      }
      
    } catch (error: any) {
      console.error(`❌ [ApiTest] Error testing company ${companyId}:`, error.message);
    }
  }
}

// Export for easy testing
export default ApiResponseTester;
