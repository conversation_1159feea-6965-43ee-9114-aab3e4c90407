/**
 * Debug utility to check company priority in API responses
 */

import companyApiService from '../services/companyApiService';

export const debugCompanyPriorityAPI = async () => {
  try {
    console.log('[CompanyPriorityDebug] Testing API response for companyPriority field...');

    // Test the /company/get-all API
    console.log('[CompanyPriorityDebug] Testing /company/get-all API...');
    const getAllResponse = await companyApiService.fetchCompaniesPage(1, 5, '');
    
    if (getAllResponse.data && getAllResponse.data.companies) {
      console.log(`[CompanyPriorityDebug] Received ${getAllResponse.data.companies.length} companies from get-all API`);
      
      getAllResponse.data.companies.forEach((company, index) => {
        console.log(`[CompanyPriorityDebug] Company ${index + 1}: ${company.companyName}`);
        console.log(`[CompanyPriorityDebug] - companyId: ${company.companyId}`);
        console.log(`[CompanyPriorityDebug] - companyPriority: ${company.companyPriority}`);
        console.log(`[CompanyPriorityDebug] - Raw company object:`, JSON.stringify(company, null, 2));
        console.log('---');
      });
    }

    // Test the /company API with a specific category
    console.log('[CompanyPriorityDebug] Testing /company API with categoryId=601...');
    const categoryResponse = await companyApiService.fetchCompaniesByCategory('601', 1, 5);
    
    if (categoryResponse.data && categoryResponse.data.companies) {
      console.log(`[CompanyPriorityDebug] Received ${categoryResponse.data.companies.length} companies from category API`);
      
      categoryResponse.data.companies.forEach((company, index) => {
        console.log(`[CompanyPriorityDebug] Category Company ${index + 1}: ${company.companyName}`);
        console.log(`[CompanyPriorityDebug] - companyId: ${company.companyId}`);
        console.log(`[CompanyPriorityDebug] - companyPriority: ${company.companyPriority}`);
        console.log(`[CompanyPriorityDebug] - Raw company object:`, JSON.stringify(company, null, 2));
        console.log('---');
      });
    }

    console.log('[CompanyPriorityDebug] API debug test completed.');

  } catch (error) {
    console.error('[CompanyPriorityDebug] Error testing API:', error);
  }
};

export const debugDatabaseCompanyPriority = async () => {
  try {
    console.log('[CompanyPriorityDebug] Testing database company priority...');
    
    const watermelonCompanyRepository = (await import('../database/watermelon/repositories/companyRepository')).default;
    
    // Get first 5 companies from database
    const companies = await watermelonCompanyRepository.getAll();
    const first5 = companies.slice(0, 5);
    
    console.log(`[CompanyPriorityDebug] Found ${companies.length} companies in database, showing first 5:`);
    
    first5.forEach((company, index) => {
      console.log(`[CompanyPriorityDebug] DB Company ${index + 1}: ${company.company_name}`);
      console.log(`[CompanyPriorityDebug] - company_id: ${company.company_id}`);
      console.log(`[CompanyPriorityDebug] - company_priority: ${company.company_priority}`);
      console.log('---');
    });

    // Test the new sorted method
    console.log('[CompanyPriorityDebug] Testing getAllSortedByPriority method...');
    const sortedCompanies = await watermelonCompanyRepository.getAllSortedByPriority();
    const first5Sorted = sortedCompanies.slice(0, 5);
    
    console.log(`[CompanyPriorityDebug] Sorted companies (first 5):`);
    first5Sorted.forEach((company, index) => {
      console.log(`[CompanyPriorityDebug] Sorted ${index + 1}: ${company.company_name} - Priority: ${company.company_priority}`);
    });

    console.log('[CompanyPriorityDebug] Database debug test completed.');

  } catch (error) {
    console.error('[CompanyPriorityDebug] Error testing database:', error);
  }
};

export const debugFullCompanyPriority = async () => {
  console.log('[CompanyPriorityDebug] Starting full company priority debug...');
  await debugCompanyPriorityAPI();
  await debugDatabaseCompanyPriority();
  console.log('[CompanyPriorityDebug] Full debug completed.');
};

export default debugFullCompanyPriority;
