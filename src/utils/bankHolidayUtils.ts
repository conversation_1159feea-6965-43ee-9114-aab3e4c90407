/**
 * Bank Holiday Utility Functions
 *
 * This utility provides functions to detect bank holidays and generate
 * appropriate messages for companies in the Bank category (categoryId = 492).
 *
 * Bank holidays include:
 * - 2nd and 4th Saturdays of each month
 * - National holidays: Republic Day (Jan 26), Independence Day (Aug 15), <PERSON> (Oct 2)
 */

export interface BankHolidayStatus {
  isHoliday: boolean;
  message: string;
}



/**
 * Calculate the nth Saturday of a given month
 * @param year - The year
 * @param month - The month (0-11, JavaScript Date format)
 * @param n - Which Saturday (1st, 2nd, 3rd, 4th)
 * @returns Date object representing the nth Saturday
 */
export const getNthSaturdayOfMonth = (
  year: number,
  month: number,
  n: number,
): Date => {
  const firstDay = new Date(year, month, 1);
  const firstSaturday = new Date(firstDay);

  // Find first Saturday of the month
  const daysToSaturday = (6 - firstDay.getDay()) % 7;
  firstSaturday.setDate(1 + daysToSaturday);

  // Calculate nth Saturday
  const nthSaturday = new Date(firstSaturday);
  nthSaturday.setDate(firstSaturday.getDate() + (n - 1) * 7);

  return nthSaturday;
};

/**
 * Check if a given date is a national holiday
 * @param date - The date to check
 * @returns Holiday name if it's a national holiday, null otherwise
 */
export const isNationalHoliday = (date: Date): string | null => {
  const day = date.getDate();
  const month = date.getMonth() + 1; // getMonth() returns 0-11

  if (month === 1 && day === 26) return 'Republic Day';
  if (month === 8 && day === 15) return 'Independence Day';
  if (month === 10 && day === 2) return 'Gandhi Jayanti';

  return null;
};

/**
 * Check if a given date is a bank holiday
 * @param date - The date to check (defaults to today)
 * @returns Object with holiday status and message
 */
export const isBankHoliday = (date: Date = new Date()): BankHolidayStatus => {
  const year = date.getFullYear();
  const month = date.getMonth();
  const day = date.getDate();

  // Check for national holidays
  const nationalHoliday = isNationalHoliday(date);
  if (nationalHoliday) {
    return {
      isHoliday: true,
      message: `Banks are closed today for ${nationalHoliday}`,
    };
  }

  // Check for 2nd and 4th Saturday
  const secondSaturday = getNthSaturdayOfMonth(year, month, 2);
  const fourthSaturday = getNthSaturdayOfMonth(year, month, 4);

  if (day === secondSaturday.getDate() && date.getDay() === 6) {
    return {
      isHoliday: true,
      message: 'Banks are closed today (2nd Saturday of the month)',
    };
  }

  if (day === fourthSaturday.getDate() && date.getDay() === 6) {
    return {
      isHoliday: true,
      message: 'Banks are closed today (4th Saturday of the month)',
    };
  }

  return { isHoliday: false, message: '' };
};



/**
 * Check if a category ID represents the Bank category
 * @param categoryId - The category ID to check
 * @returns True if categoryId is 492 (Bank category)
 */
export const isBankCategoryId = (categoryId: number): boolean => {
  return categoryId === 492;
};



/**
 * Get bank holiday status for a category ID
 * @param categoryId - The category ID to check
 * @param date - Date to check (defaults to today)
 * @returns Object with bank category status, holiday status, and message
 */
export const getBankHolidayStatusForCategory = (
  categoryId: number,
  date: Date = new Date(),
): {
  isBankCategory: boolean;
  isHoliday: boolean;
  message: string;
} => {
  const isBankCat = isBankCategoryId(categoryId);
  
  if (!isBankCat) {
    return {
      isBankCategory: false,
      isHoliday: false,
      message: '',
    };
  }

  const holidayStatus = isBankHoliday(date);
  
  return {
    isBankCategory: true,
    isHoliday: holidayStatus.isHoliday,
    message: holidayStatus.message,
  };
};

/**
 * Test function to verify bank holiday logic
 * @param date - Date to test
 * @param categoryId - Category ID to test (defaults to 492 for Banks)
 */
export const testBankHoliday = (date: Date, categoryId: number = 492) => {
  console.log(`\n=== Testing Bank Holiday Logic ===`);
  console.log(`Date: ${date.toDateString()}`);
  console.log(`Day of week: ${date.getDay()} (0=Sunday, 6=Saturday)`);
  console.log(`Category ID: ${categoryId}`);

  // Test the basic holiday detection
  const holidayStatus = isBankHoliday(date);
  console.log(`Holiday Status:`, holidayStatus);

  // Test for category
  const categoryStatus = getBankHolidayStatusForCategory(categoryId, date);
  console.log(`Category Status:`, categoryStatus);

  // Test 2nd Saturday calculation
  const year = date.getFullYear();
  const month = date.getMonth();
  const secondSaturday = getNthSaturdayOfMonth(year, month, 2);
  const fourthSaturday = getNthSaturdayOfMonth(year, month, 4);

  console.log(`2nd Saturday of ${year}-${month + 1}: ${secondSaturday.toDateString()}`);
  console.log(`4th Saturday of ${year}-${month + 1}: ${fourthSaturday.toDateString()}`);
  console.log(`Is test date the 2nd Saturday? ${date.getDate() === secondSaturday.getDate() && date.getDay() === 6}`);
  console.log(`Is test date the 4th Saturday? ${date.getDate() === fourthSaturday.getDate() && date.getDay() === 6}`);
  console.log(`================================\n`);
};
