/**
 * Utility to fix history migration issues
 * This helps when the app is trying to use number_id column before migration is applied
 */

import database from '../database/watermelon/database';
import { Q } from '@nozbe/watermelondb';

export class HistoryMigrationFixer {
  /**
   * Check if the number_id column exists in the history table
   */
  static async checkNumberIdColumnExists(): Promise<boolean> {
    try {
      const historyCollection = database.get('history');
      
      // Try to query with number_id column
      await historyCollection.query(Q.where('number_id', 0)).fetch();
      console.log('✅ number_id column exists in history table');
      return true;
    } catch (error: any) {
      if (error.message && error.message.includes('no such column: history.number_id')) {
        console.log('❌ number_id column does not exist in history table');
        return false;
      }
      // Some other error
      console.error('Error checking number_id column:', error);
      return false;
    }
  }

  /**
   * Get current database schema version
   */
  static async getCurrentSchemaVersion(): Promise<number> {
    try {
      // WatermelonDB stores schema version internally
      // We can check by trying to access different columns
      
      const historyCollection = database.get('history');
      const sampleItems = await historyCollection.query(Q.take(1)).fetch();
      
      if (sampleItems.length === 0) {
        console.log('No history items found, cannot determine schema version');
        return 0;
      }
      
      const item = sampleItems[0];
      
      // Check for different schema versions based on available fields
      try {
        // Try to access number_id (schema version 8)
        const numberId = (item as any).numberId;
        console.log('Schema appears to be version 8 (has number_id)');
        return 8;
      } catch (error) {
        try {
          // Try to access company_name (schema version 8 without migration applied)
          const companyName = (item as any).companyName;
          console.log('Schema appears to be version 8 (has company_name but no number_id)');
          return 7; // Partial migration
        } catch (error2) {
          console.log('Schema appears to be older version');
          return 7;
        }
      }
    } catch (error) {
      console.error('Error determining schema version:', error);
      return 0;
    }
  }

  /**
   * Force database reset to apply migrations
   */
  static async forceDatabaseReset(): Promise<void> {
    try {
      console.log('🔄 Starting database reset to apply migrations...');
      
      // Clear all collections
      const collections = ['history', 'companies', 'categories', 'numbers', 'company_categories', 'sync_state', 'notes'];
      
      for (const collectionName of collections) {
        try {
          const collection = database.get(collectionName);
          const allItems = await collection.query().fetch();
          
          if (allItems.length > 0) {
            await database.write(async () => {
              const batch = allItems.map(item => item.prepareDestroyPermanently());
              await database.batch(batch);
            });
            console.log(`✅ Cleared ${allItems.length} items from ${collectionName}`);
          }
        } catch (error) {
          console.log(`⚠️ Could not clear ${collectionName}:`, error);
        }
      }
      
      console.log('✅ Database reset completed. Restart the app to apply migrations.');
    } catch (error) {
      console.error('❌ Error during database reset:', error);
      throw error;
    }
  }

  /**
   * Run diagnostic checks
   */
  static async runDiagnostics(): Promise<void> {
    try {
      console.log('🔍 Running history migration diagnostics...\n');
      
      // Check schema version
      const version = await this.getCurrentSchemaVersion();
      console.log(`Current schema version: ${version}`);
      
      // Check if number_id column exists
      const hasNumberId = await this.checkNumberIdColumnExists();
      console.log(`number_id column exists: ${hasNumberId}`);
      
      // Check history table contents
      const historyCollection = database.get('history');
      const historyCount = await historyCollection.query().fetchCount();
      console.log(`History entries count: ${historyCount}`);
      
      if (historyCount > 0) {
        const sampleHistory = await historyCollection.query(Q.take(3)).fetch();
        console.log('Sample history entries:');
        sampleHistory.forEach((item, index) => {
          console.log(`  ${index + 1}. ID: ${item.id}, companyId: ${(item as any).companyId}`);
        });
      }
      
      // Recommendations
      console.log('\n📋 Recommendations:');
      if (!hasNumberId && version >= 8) {
        console.log('❗ Migration issue detected: Schema version suggests number_id should exist but column is missing');
        console.log('💡 Recommendation: Run forceDatabaseReset() to clear data and apply migrations');
      } else if (hasNumberId) {
        console.log('✅ Migration appears to be working correctly');
      } else {
        console.log('ℹ️ App is using fallback logic for missing number_id column');
      }
      
      console.log('\n✅ Diagnostics completed');
    } catch (error) {
      console.error('❌ Error running diagnostics:', error);
      throw error;
    }
  }
}

export default HistoryMigrationFixer;
