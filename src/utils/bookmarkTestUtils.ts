import bookmarkStorageService from '../services/bookmarkStorageService';

/**
 * Utility functions for testing bookmark functionality
 */
export class BookmarkTestUtils {
  /**
   * Test adding a bookmark
   */
  static async testAddBookmark(companyId: number, companyName: string): Promise<void> {
    console.log(`[BookmarkTest] Testing add bookmark for ${companyName} (ID: ${companyId})`);
    
    try {
      await bookmarkStorageService.addBookmark(companyId, companyName, false);
      const isBookmarked = await bookmarkStorageService.isCompanyBookmarked(companyId);
      
      console.log(`[BookmarkTest] Add bookmark result: ${isBookmarked ? 'SUCCESS' : 'FAILED'}`);
      return;
    } catch (error) {
      console.error('[BookmarkTest] Add bookmark failed:', error);
      throw error;
    }
  }

  /**
   * Test removing a bookmark
   */
  static async testRemoveBookmark(companyId: number): Promise<void> {
    console.log(`[BookmarkTest] Testing remove bookmark for company ID: ${companyId}`);
    
    try {
      await bookmarkStorageService.removeBookmark(companyId);
      const isBookmarked = await bookmarkStorageService.isCompanyBookmarked(companyId);
      
      console.log(`[BookmarkTest] Remove bookmark result: ${!isBookmarked ? 'SUCCESS' : 'FAILED'}`);
      return;
    } catch (error) {
      console.error('[BookmarkTest] Remove bookmark failed:', error);
      throw error;
    }
  }

  /**
   * Test shortcut state management
   */
  static async testShortcutState(companyId: number): Promise<void> {
    console.log(`[BookmarkTest] Testing shortcut state for company ID: ${companyId}`);
    
    try {
      const shortcutId = `company_${companyId}`;
      
      // Add shortcut
      await bookmarkStorageService.addShortcut(companyId, shortcutId);
      let hasShortcut = await bookmarkStorageService.hasShortcut(companyId);
      console.log(`[BookmarkTest] Add shortcut result: ${hasShortcut ? 'SUCCESS' : 'FAILED'}`);
      
      // Update shortcut status
      await bookmarkStorageService.updateShortcutStatus(companyId, true, shortcutId);
      
      // Remove shortcut
      await bookmarkStorageService.removeShortcut(companyId);
      hasShortcut = await bookmarkStorageService.hasShortcut(companyId);
      console.log(`[BookmarkTest] Remove shortcut result: ${!hasShortcut ? 'SUCCESS' : 'FAILED'}`);
      
      return;
    } catch (error) {
      console.error('[BookmarkTest] Shortcut state test failed:', error);
      throw error;
    }
  }

  /**
   * Test complete bookmark workflow
   */
  static async testCompleteWorkflow(companyId: number, companyName: string): Promise<void> {
    console.log(`[BookmarkTest] Testing complete workflow for ${companyName} (ID: ${companyId})`);
    
    try {
      // 1. Add bookmark
      await this.testAddBookmark(companyId, companyName);
      
      // 2. Test shortcut state
      await this.testShortcutState(companyId);
      
      // 3. Remove bookmark
      await this.testRemoveBookmark(companyId);
      
      console.log('[BookmarkTest] Complete workflow test: SUCCESS');
    } catch (error) {
      console.error('[BookmarkTest] Complete workflow test failed:', error);
      throw error;
    }
  }

  /**
   * Get all bookmarked companies for debugging
   */
  static async debugBookmarks(): Promise<void> {
    try {
      const bookmarks = await bookmarkStorageService.getBookmarkedCompanies();
      const shortcuts = await bookmarkStorageService.getCompanyShortcuts();
      
      console.log('[BookmarkTest] Current bookmarks:', bookmarks);
      console.log('[BookmarkTest] Current shortcuts:', shortcuts);
    } catch (error) {
      console.error('[BookmarkTest] Debug bookmarks failed:', error);
    }
  }

  /**
   * Clear all test data
   */
  static async clearTestData(): Promise<void> {
    try {
      await bookmarkStorageService.clearAll();
      console.log('[BookmarkTest] Test data cleared successfully');
    } catch (error) {
      console.error('[BookmarkTest] Clear test data failed:', error);
      throw error;
    }
  }
}

// Export for easy testing in console
export default BookmarkTestUtils;
