/**
 * Debug utility to check vote counts in the numbers table
 */

import watermelonNumberRepository from '../database/watermelon/repositories/numberRepository';
import watermelonCompanyRepository from '../database/watermelon/repositories/companyRepository';
import companyApiService from '../services/companyApiService';

export class NumberVoteDebugger {
  
  /**
   * Check vote counts in the numbers table (database only)
   */
  async debugNumberVotesOffline() {
    console.log('\n🔍 === NUMBER VOTE COUNT DEBUGGING (OFFLINE) ===');

    try {
      // 1. Check database numbers
      console.log('\n📊 Step 1: Checking numbers in database...');
      const dbNumbers = await watermelonNumberRepository.getAll();
      console.log(`Found ${dbNumbers.length} numbers in database`);

      if (dbNumbers.length === 0) {
        console.log('❌ No numbers found in database. Run company sync first.');
        return;
      }
      
      // Show vote count stats from database
      let numbersWithUpvotes = 0;
      let numbersWithDownvotes = 0;
      let totalUpvotes = 0;
      let totalDownvotes = 0;
      
      dbNumbers.forEach(number => {
        if (number.upvote_count && number.upvote_count > 0) {
          numbersWithUpvotes++;
          totalUpvotes += number.upvote_count;
        }
        if (number.downvote_count && number.downvote_count > 0) {
          numbersWithDownvotes++;
          totalDownvotes += number.downvote_count;
        }
      });
      
      console.log(`📈 Database number vote stats:`);
      console.log(`  Numbers with upvotes: ${numbersWithUpvotes}/${dbNumbers.length}`);
      console.log(`  Numbers with downvotes: ${numbersWithDownvotes}/${dbNumbers.length}`);
      console.log(`  Total upvotes: ${totalUpvotes}`);
      console.log(`  Total downvotes: ${totalDownvotes}`);
      
      // Show sample numbers with their vote counts
      console.log(`\n📋 Sample numbers from database (first 10):`);
      dbNumbers.slice(0, 10).forEach((number, index) => {
        console.log(`  ${index + 1}. ${number.number} (${number.type}) - Company ID: ${number.company_id}`);
        console.log(`     Upvotes: ${number.upvote_count || 0}, Downvotes: ${number.downvote_count || 0}`);
        console.log(`     Description: ${number.description || 'N/A'}`);
        console.log(`     API Number ID: ${number.api_number_id}`);
      });

      console.log('\n✅ Number vote count debugging completed (offline mode)');

    } catch (error) {
      console.error('❌ Error during number vote count debugging:', error);
      throw error;
    }
  }

  /**
   * Check vote counts in the numbers table (with API comparison)
   */
  async debugNumberVotes() {
    console.log('\n🔍 === NUMBER VOTE COUNT DEBUGGING (WITH API) ===');

    try {
      // First run offline check
      await this.debugNumberVotesOffline();

      // Then try API comparison
      console.log('\n🌐 Step 2: Checking API data...');
      const apiResponse = await companyApiService.fetchCompaniesPage(1, 5);
      const apiCompanies = apiResponse.data.companies;
      
      console.log(`📋 Sample numbers from API (first 5 companies):`);
      apiCompanies.forEach((company, companyIndex) => {
        console.log(`\n  Company ${companyIndex + 1}: ${company.companyName} (ID: ${company.companyId})`);
        
        if (typeof company.number === 'object' && company.number !== null) {
          const numberObj = company.number;
          
          // Check TOLL_FREE numbers
          if (numberObj.TOLL_FREE && Array.isArray(numberObj.TOLL_FREE)) {
            numberObj.TOLL_FREE.forEach((num, numIndex) => {
              console.log(`    TOLL_FREE ${numIndex + 1}: ${num.number}`);
              console.log(`      API upvoteCount: ${num.upvoteCount || 0}, API downvoteCount: ${num.downvoteCount || 0}`);
            });
          }
          
          // Check ALL_INDIA numbers
          if (numberObj.ALL_INDIA && Array.isArray(numberObj.ALL_INDIA)) {
            numberObj.ALL_INDIA.forEach((num, numIndex) => {
              console.log(`    ALL_INDIA ${numIndex + 1}: ${num.number}`);
              console.log(`      API upvoteCount: ${num.upvoteCount || 0}, API downvoteCount: ${num.downvoteCount || 0}`);
            });
          }
          
          // Check INTERNATIONAL numbers
          if (numberObj.INTERNATIONAL && Array.isArray(numberObj.INTERNATIONAL)) {
            numberObj.INTERNATIONAL.forEach((num, numIndex) => {
              console.log(`    INTERNATIONAL ${numIndex + 1}: ${num.number}`);
              console.log(`      API upvoteCount: ${num.upvoteCount || 0}, API downvoteCount: ${num.downvoteCount || 0}`);
            });
          }
        } else {
          console.log(`    Number field: ${company.number || 'N/A'} (old format)`);
        }
      });
      
      console.log('\n✅ Number vote count debugging completed (with API comparison)');

    } catch (error) {
      console.error('❌ Error during API comparison (database check was successful):', error);
      console.log('💡 Try the offline version if you have network issues');
    }
  }
  
  /**
   * Check numbers for a specific company
   */
  async debugCompanyNumbers(companyId: number) {
    console.log(`\n🔍 Debugging numbers for company ID: ${companyId}`);
    
    try {
      // Check database
      const dbNumbers = await watermelonNumberRepository.getByCompanyId(companyId);
      console.log(`📊 Database numbers for company ${companyId}:`);
      
      if (dbNumbers.length === 0) {
        console.log(`❌ No numbers found for company ID ${companyId} in database`);
      } else {
        dbNumbers.forEach((number, index) => {
          console.log(`  ${index + 1}. ${number.number} (${number.type})`);
          console.log(`     Upvotes: ${number.upvote_count || 0}, Downvotes: ${number.downvote_count || 0}`);
          console.log(`     Description: ${number.description || 'N/A'}`);
          console.log(`     API Number ID: ${number.api_number_id}`);
        });
      }
      
      // Check API
      console.log(`\n🌐 Fetching from API...`);
      const apiResponse = await companyApiService.fetchCompaniesPage(1, 100);
      const apiCompany = apiResponse.data.companies.find(c => c.companyId === companyId);
      
      if (apiCompany) {
        console.log(`📊 API numbers for company ${companyId} (${apiCompany.companyName}):`);
        
        if (typeof apiCompany.number === 'object' && apiCompany.number !== null) {
          const numberObj = apiCompany.number;
          let numberCount = 0;
          
          // Check all number types
          ['TOLL_FREE', 'ALL_INDIA', 'INTERNATIONAL'].forEach(type => {
            const numbers = numberObj[type as keyof typeof numberObj];
            if (numbers && Array.isArray(numbers)) {
              numbers.forEach((num, index) => {
                numberCount++;
                console.log(`  ${numberCount}. ${num.number} (${type})`);
                console.log(`     API upvoteCount: ${num.upvoteCount || 0}, API downvoteCount: ${num.downvoteCount || 0}`);
                console.log(`     Description: ${num.description || 'N/A'}`);
                console.log(`     API Number ID: ${num.numberId}`);
              });
            }
          });
          
          if (numberCount === 0) {
            console.log(`  No numbers found in API response`);
          }
        } else {
          console.log(`  Number field: ${apiCompany.number || 'N/A'} (old format)`);
        }
      } else {
        console.log(`❌ Company ID ${companyId} not found in API response`);
      }
      
    } catch (error) {
      console.error('❌ Error debugging company numbers:', error);
      throw error;
    }
  }
  
  /**
   * Get summary statistics
   */
  async getVoteStatistics() {
    console.log('\n📊 === VOTE STATISTICS SUMMARY ===');
    
    try {
      const dbNumbers = await watermelonNumberRepository.getAll();
      const dbCompanies = await watermelonCompanyRepository.getAll();
      
      console.log(`\n📈 Database Statistics:`);
      console.log(`  Total companies: ${dbCompanies.length}`);
      console.log(`  Total numbers: ${dbNumbers.length}`);
      
      // Number vote statistics
      const numberStats = dbNumbers.reduce((stats, number) => {
        stats.totalUpvotes += number.upvote_count || 0;
        stats.totalDownvotes += number.downvote_count || 0;
        if ((number.upvote_count || 0) > 0) stats.numbersWithUpvotes++;
        if ((number.downvote_count || 0) > 0) stats.numbersWithDownvotes++;
        return stats;
      }, { totalUpvotes: 0, totalDownvotes: 0, numbersWithUpvotes: 0, numbersWithDownvotes: 0 });
      
      console.log(`\n📞 Number Vote Statistics:`);
      console.log(`  Numbers with upvotes: ${numberStats.numbersWithUpvotes}/${dbNumbers.length}`);
      console.log(`  Numbers with downvotes: ${numberStats.numbersWithDownvotes}/${dbNumbers.length}`);
      console.log(`  Total upvotes across all numbers: ${numberStats.totalUpvotes}`);
      console.log(`  Total downvotes across all numbers: ${numberStats.totalDownvotes}`);
      
      // Company vote statistics (these should be mostly 0 since votes are on numbers)
      const companyStats = dbCompanies.reduce((stats, company) => {
        stats.totalUpvotes += company.upvote_count || 0;
        stats.totalDownvotes += company.downvote_count || 0;
        if ((company.upvote_count || 0) > 0) stats.companiesWithUpvotes++;
        if ((company.downvote_count || 0) > 0) stats.companiesWithDownvotes++;
        return stats;
      }, { totalUpvotes: 0, totalDownvotes: 0, companiesWithUpvotes: 0, companiesWithDownvotes: 0 });
      
      console.log(`\n🏢 Company Vote Statistics (should be mostly 0):`);
      console.log(`  Companies with upvotes: ${companyStats.companiesWithUpvotes}/${dbCompanies.length}`);
      console.log(`  Companies with downvotes: ${companyStats.companiesWithDownvotes}/${dbCompanies.length}`);
      console.log(`  Total upvotes across all companies: ${companyStats.totalUpvotes}`);
      console.log(`  Total downvotes across all companies: ${companyStats.totalDownvotes}`);
      
    } catch (error) {
      console.error('❌ Error getting vote statistics:', error);
      throw error;
    }
  }
}

export default new NumberVoteDebugger();
