import Toast from 'react-native-toast-message';
import {IOS} from '../common';

export const showSuccessToast = message => {
  const config = {
    position: 'top',
    type: 'info',
    text1: message,
    topOffset: IOS ? 20 : 50,
  };
  Toast.show(config);
};

export const showErrorToast = error => {
  const config = {
    position: 'top',
    type: 'error',
    text1:
      typeof error === 'string' ? error : error?.message || 'An error occurred',
    topOffset: IOS ? 20 : 50,
  };
  Toast.show(config);
};

export const showWarningToast = message => {
  const config = {
    position: 'top',
    type: 'warning',
    text1: message,
    topOffset: IOS ? 20 : 50,
  };
  Toast.show(config);
};

export const showTomatoToast = (message, error) => {
  const config = {
    position: 'top',
    type: 'tomatoToast',
    text1: message,
    props: error,
    topOffset: IOS ? 20 : 50,
  };
  Toast.show(config);
};
