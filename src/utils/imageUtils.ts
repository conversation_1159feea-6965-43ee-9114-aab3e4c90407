/**
 * Utility functions for image handling and validation
 */

/**
 * <PERSON><PERSON>ly encodes URL path components while preserving the base URL structure
 * @param url - The URL to encode
 * @returns Properly encoded URL
 */
export const encodeImageUrl = (url: string): string => {
  if (!url || typeof url !== 'string') {
    return url;
  }

  try {
    // Split URL into base and path
    const protocolEndIndex = url.indexOf('://') + 3;
    const pathStartIndex = url.indexOf('/', protocolEndIndex);

    if (pathStartIndex === -1) {
      // No path, return as is
      return url;
    }

    const baseUrl = url.substring(0, pathStartIndex);
    const path = url.substring(pathStartIndex);

    // Encode only the path part, preserving forward slashes
    const encodedPath = path.split('/').map(segment =>
      segment ? encodeURIComponent(segment) : segment
    ).join('/');

    return baseUrl + encodedPath;
  } catch (error) {
    console.warn('[ImageUtils] Error encoding URL:', url, error);
    return url;
  }
};

/**
 * Validates if a given URL is a valid image URL
 * @param url - The URL to validate
 * @returns boolean indicating if the URL is valid
 */
export const isValidImageUrl = (url: string | null | undefined): boolean => {
  if (!url || typeof url !== 'string') {
    return false;
  }

  // Trim whitespace
  const trimmedUrl = url.trim();

  // Check if URL starts with http or https
  if (!trimmedUrl.startsWith('http://') && !trimmedUrl.startsWith('https://')) {
    console.warn('[ImageUtils] Invalid protocol for image URL:', url);
    return false;
  }

  // Check for basic URL structure using regex (allow spaces in path for image filenames)
  const urlRegex = /^https?:\/\/[^\s/$.?#].[^]*$/i;
  if (!urlRegex.test(trimmedUrl)) {
    console.warn('[ImageUtils] Invalid URL format:', url);
    return false;
  }

  // Check for common malformed patterns
  if (trimmedUrl.includes('www/') || trimmedUrl.includes('//www/')) {
    console.warn('[ImageUtils] Malformed hostname detected:', url);
    return false;
  }

  // Check for minimum length (should be longer than just protocol)
  if (trimmedUrl.length < 12) { // "https://a.b" is 11 chars
    console.warn('[ImageUtils] URL too short:', url);
    return false;
  }

  // Extract hostname part for additional validation
  const protocolEndIndex = trimmedUrl.indexOf('://') + 3;
  const hostnameEndIndex = trimmedUrl.indexOf('/', protocolEndIndex);
  const hostname = hostnameEndIndex === -1
    ? trimmedUrl.substring(protocolEndIndex)
    : trimmedUrl.substring(protocolEndIndex, hostnameEndIndex);

  // Check hostname validity
  if (!hostname || hostname.length < 3) {
    console.warn('[ImageUtils] Invalid hostname for image URL:', url);
    return false;
  }

  // Check for valid hostname characters
  const hostnameRegex = /^[a-zA-Z0-9.-]+$/;
  if (!hostnameRegex.test(hostname)) {
    console.warn('[ImageUtils] Invalid hostname characters:', url);
    return false;
  }

  return true;
};

/**
 * Creates a standardized error handler for image loading errors
 * @param componentName - Name of the component for logging
 * @param imageUrl - The URL that failed to load
 * @param additionalData - Additional data to log (e.g., item name, ID)
 * @param fallbackCallback - Callback to execute on error (e.g., set fallback image)
 */
export const createImageErrorHandler = (
  componentName: string,
  imageUrl: string | null | undefined,
  additionalData: Record<string, any> = {},
  fallbackCallback?: () => void,
) => {
  return (error: any) => {
    const errorMessage = error.nativeEvent?.error || 'Unknown error';
    const is404Error = errorMessage.includes('404') || errorMessage.includes('Not Found');

    /*
    // Enhanced logging with error analysis
    console.error(`[${componentName}] Image Load Error:`, {
      url: imageUrl,
      originalUrl: imageUrl,
      encodedUrl: imageUrl ? encodeImageUrl(imageUrl) : null,
      error: error.nativeEvent,
      errorType: is404Error ? '404_NOT_FOUND' : 'OTHER',
      suggestion: is404Error
        ? 'Image file may have been moved or deleted from server'
        : 'Check network connection and URL format',
      ...additionalData,
    });
    */
   
    // Additional warning for 404 errors
    if (is404Error && imageUrl) {
      console.warn(`[${componentName}] 404 Error - Image not found on server:`, imageUrl);
      console.warn(`[${componentName}] Consider updating the image URL in the database or providing a fallback`);
    }

    if (fallbackCallback) {
      fallbackCallback();
    }
  };
};

/**
 * Gets a safe image source object with validation and encoding
 * @param url - The image URL
 * @param fallbackSource - The fallback image source (local asset)
 * @returns Image source object
 */
export const getSafeImageSource = (
  url: string | null | undefined,
  fallbackSource: any,
) => {
  if (url && isValidImageUrl(url)) {
    // Encode the URL to handle spaces and special characters
    const encodedUrl = encodeImageUrl(url);
    return { uri: encodedUrl };
  }
  return fallbackSource;
};
