/**
 * Network connectivity test utility
 */

import axios from 'axios';
import { CONFIG } from '../common/constant';

export class NetworkTester {
  
  /**
   * Test basic network connectivity
   */
  async testNetworkConnectivity() {
    console.log('\n🌐 === NETWORK CONNECTIVITY TEST ===');
    
    try {
      // Test 1: Basic internet connectivity
      console.log('\n📡 Step 1: Testing basic internet connectivity...');
      try {
        const response = await axios.get('https://httpbin.org/get', { timeout: 5000 });
        console.log('✅ Basic internet connectivity: OK');
        console.log(`   Response status: ${response.status}`);
      } catch (error) {
        console.log('❌ Basic internet connectivity: FAILED');
        console.log(`   Error: ${error}`);
        return;
      }
      
      // Test 2: API server connectivity
      console.log('\n🔗 Step 2: Testing API server connectivity...');
      console.log(`   API URL: ${CONFIG.API_URL}`);
      
      try {
        const response = await axios.get(`${CONFIG.API_URL}/health`, { timeout: 10000 });
        console.log('✅ API server connectivity: OK');
        console.log(`   Response status: ${response.status}`);
      } catch (error) {
        console.log('❌ API server health check: FAILED');
        console.log(`   Error: ${error}`);
        
        // Try a different endpoint
        console.log('\n🔄 Trying alternative endpoint...');
        try {
          const altResponse = await axios.get(`${CONFIG.API_URL}/categories`, { timeout: 10000 });
          console.log('✅ Alternative endpoint (/categories): OK');
          console.log(`   Response status: ${altResponse.status}`);
        } catch (altError) {
          console.log('❌ Alternative endpoint: FAILED');
          console.log(`   Error: ${altError}`);
        }
      }
      
      // Test 3: Company API endpoint
      console.log('\n🏢 Step 3: Testing company API endpoint...');
      try {
        const response = await axios.get(`${CONFIG.API_URL}/company/get-all?page=1&limit=1`, { timeout: 15000 });
        console.log('✅ Company API endpoint: OK');
        console.log(`   Response status: ${response.status}`);
        console.log(`   Response data keys: ${Object.keys(response.data).join(', ')}`);
        
        if (response.data && response.data.data && response.data.data.companies) {
          console.log(`   Companies returned: ${response.data.data.companies.length}`);
          if (response.data.data.companies.length > 0) {
            const firstCompany = response.data.data.companies[0];
            console.log(`   First company: ${firstCompany.companyName} (ID: ${firstCompany.companyId})`);
            console.log(`   Company upVoteCount: ${firstCompany.upVoteCount || 0}`);
            console.log(`   Company downVoteCount: ${firstCompany.downVoteCount || 0}`);
            
            // Check number structure
            if (firstCompany.number && typeof firstCompany.number === 'object') {
              console.log(`   Number structure: ${Object.keys(firstCompany.number).join(', ')}`);
              
              // Check for vote counts in numbers
              let foundVotes = false;
              Object.keys(firstCompany.number).forEach(type => {
                const numbers = firstCompany.number[type];
                if (Array.isArray(numbers) && numbers.length > 0) {
                  const firstNumber = numbers[0];
                  if (firstNumber.upvoteCount !== undefined || firstNumber.downvoteCount !== undefined) {
                    console.log(`   ${type} number votes: up=${firstNumber.upvoteCount || 0}, down=${firstNumber.downvoteCount || 0}`);
                    foundVotes = true;
                  }
                }
              });
              
              if (!foundVotes) {
                console.log('   ⚠️  No vote counts found in number objects');
              }
            } else {
              console.log(`   Number field type: ${typeof firstCompany.number}`);
              console.log(`   Number value: ${firstCompany.number}`);
            }
          }
        }
      } catch (error) {
        console.log('❌ Company API endpoint: FAILED');
        console.log(`   Error: ${error}`);
      }
      
      console.log('\n✅ Network connectivity test completed');
      
    } catch (error) {
      console.error('❌ Network test failed:', error);
    }
  }
  
  /**
   * Test specific company endpoint
   */
  async testCompanyEndpoint(companyId: number) {
    console.log(`\n🎯 Testing specific company endpoint for ID: ${companyId}`);
    
    try {
      const response = await axios.get(`${CONFIG.API_URL}/company/get-all?page=1&limit=100`, { timeout: 15000 });
      
      if (response.data && response.data.data && response.data.data.companies) {
        const company = response.data.data.companies.find((c: any) => c.companyId === companyId);
        
        if (company) {
          console.log(`✅ Found company: ${company.companyName}`);
          console.log(`   Company ID: ${company.companyId}`);
          console.log(`   Company upVoteCount: ${company.upVoteCount || 0}`);
          console.log(`   Company downVoteCount: ${company.downVoteCount || 0}`);
          
          if (company.number && typeof company.number === 'object') {
            console.log(`   Number types: ${Object.keys(company.number).join(', ')}`);
            
            Object.keys(company.number).forEach(type => {
              const numbers = company.number[type];
              if (Array.isArray(numbers)) {
                console.log(`   ${type}: ${numbers.length} numbers`);
                numbers.forEach((num: any, index: number) => {
                  console.log(`     ${index + 1}. ${num.number} - up:${num.upvoteCount || 0}, down:${num.downvoteCount || 0}`);
                });
              }
            });
          }
        } else {
          console.log(`❌ Company ID ${companyId} not found in API response`);
        }
      }
    } catch (error) {
      console.log(`❌ Error testing company endpoint: ${error}`);
    }
  }
  
  /**
   * Get current network status
   */
  async getNetworkStatus() {
    console.log('\n📊 === NETWORK STATUS ===');
    
    try {
      console.log(`🔗 API Base URL: ${CONFIG.API_URL}`);
      console.log(`📱 Device Type: ${CONFIG.DEVICE_TYPE}`);
      console.log(`📦 App Version: ${CONFIG.APP_VERSION}`);
      
      // Quick ping test
      const startTime = Date.now();
      try {
        await axios.get('https://httpbin.org/get', { timeout: 3000 });
        const endTime = Date.now();
        console.log(`⚡ Network latency: ${endTime - startTime}ms`);
        console.log('🟢 Network status: ONLINE');
      } catch (error) {
        console.log('🔴 Network status: OFFLINE');
        console.log(`   Error: ${error}`);
      }
      
    } catch (error) {
      console.error('❌ Error getting network status:', error);
    }
  }
}

export default new NetworkTester();
