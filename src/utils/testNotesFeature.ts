/**
 * Test utility for the Notes feature
 * This utility helps test the complete notes functionality including:
 * - Creating notes
 * - Reading notes
 * - Deleting notes
 * - Data persistence
 */

import noteRepository, { NoteData } from '../database/watermelon/repositories/noteRepository';

export class NotesFeatureTester {
  private testCompanyId = 'test_company_123';

  /**
   * Run all tests for the Notes feature
   */
  async runAllTests(): Promise<void> {
    console.log('🧪 Starting Notes Feature Tests...');
    
    try {
      await this.testCreateNote();
      await this.testReadNotes();
      await this.testDeleteNote();
      await this.testMultipleNotes();
      await this.testNotesCount();
      await this.cleanup();
      
      console.log('✅ All Notes Feature Tests Passed!');
    } catch (error) {
      console.error('❌ Notes Feature Tests Failed:', error);
      throw error;
    }
  }

  /**
   * Test creating a note
   */
  async testCreateNote(): Promise<void> {
    console.log('📝 Testing note creation...');
    
    const noteData: Omit<NoteData, 'id' | 'createdAt'> = {
      noteText: 'This is a test note for the company',
      companyId: this.testCompanyId,
      deleted: false,
    };

    const noteId = await noteRepository.createNote(noteData);
    
    if (!noteId) {
      throw new Error('Failed to create note - no ID returned');
    }
    
    console.log('✅ Note created successfully with ID:', noteId);
  }

  /**
   * Test reading notes
   */
  async testReadNotes(): Promise<void> {
    console.log('📖 Testing note reading...');
    
    const notes = await noteRepository.getNotesByCompanyId(this.testCompanyId);
    
    if (notes.length === 0) {
      throw new Error('No notes found for test company');
    }
    
    const firstNote = notes[0];
    if (!firstNote.noteText || !firstNote.companyId) {
      throw new Error('Note data is incomplete');
    }
    
    console.log(`✅ Successfully read ${notes.length} note(s)`);
    console.log('First note:', firstNote.noteText);
  }

  /**
   * Test deleting a note
   */
  async testDeleteNote(): Promise<void> {
    console.log('🗑️ Testing note deletion...');
    
    // Get existing notes
    const notesBefore = await noteRepository.getNotesByCompanyId(this.testCompanyId);
    
    if (notesBefore.length === 0) {
      throw new Error('No notes available to delete');
    }
    
    const noteToDelete = notesBefore[0];
    const success = await noteRepository.deleteNote(noteToDelete.id!);
    
    if (!success) {
      throw new Error('Failed to delete note');
    }
    
    // Verify deletion
    const notesAfter = await noteRepository.getNotesByCompanyId(this.testCompanyId);
    
    if (notesAfter.length >= notesBefore.length) {
      throw new Error('Note was not properly deleted');
    }
    
    console.log('✅ Note deleted successfully');
  }

  /**
   * Test multiple notes functionality
   */
  async testMultipleNotes(): Promise<void> {
    console.log('📚 Testing multiple notes...');
    
    // Create multiple notes
    const notesToCreate = [
      'First test note',
      'Second test note',
      'Third test note with more content',
    ];
    
    for (const noteText of notesToCreate) {
      const noteData: Omit<NoteData, 'id' | 'createdAt'> = {
        noteText,
        companyId: this.testCompanyId,
        deleted: false,
      };
      
      await noteRepository.createNote(noteData);
    }
    
    // Verify all notes were created
    const notes = await noteRepository.getNotesByCompanyId(this.testCompanyId);
    
    if (notes.length < notesToCreate.length) {
      throw new Error(`Expected at least ${notesToCreate.length} notes, found ${notes.length}`);
    }
    
    console.log(`✅ Successfully created and retrieved ${notes.length} notes`);
  }

  /**
   * Test notes count functionality
   */
  async testNotesCount(): Promise<void> {
    console.log('🔢 Testing notes count...');
    
    const count = await noteRepository.getNotesCountByCompanyId(this.testCompanyId);
    const notes = await noteRepository.getNotesByCompanyId(this.testCompanyId);
    
    if (count !== notes.length) {
      throw new Error(`Count mismatch: count=${count}, actual=${notes.length}`);
    }
    
    console.log(`✅ Notes count is correct: ${count}`);
  }

  /**
   * Clean up test data
   */
  async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up test data...');
    
    const success = await noteRepository.clearNotesForCompany(this.testCompanyId);
    
    if (!success) {
      console.warn('⚠️ Failed to clean up test data');
      return;
    }
    
    // Verify cleanup
    const remainingNotes = await noteRepository.getNotesByCompanyId(this.testCompanyId);
    
    if (remainingNotes.length > 0) {
      console.warn(`⚠️ ${remainingNotes.length} notes still remain after cleanup`);
    } else {
      console.log('✅ Test data cleaned up successfully');
    }
  }

  /**
   * Test data persistence (simulate app restart)
   */
  async testDataPersistence(): Promise<void> {
    console.log('💾 Testing data persistence...');
    
    // Create a note
    const noteData: Omit<NoteData, 'id' | 'createdAt'> = {
      noteText: 'Persistence test note',
      companyId: this.testCompanyId,
      deleted: false,
    };
    
    const noteId = await noteRepository.createNote(noteData);
    
    if (!noteId) {
      throw new Error('Failed to create note for persistence test');
    }
    
    // Simulate some time passing
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Try to retrieve the note
    const retrievedNote = await noteRepository.getNoteById(noteId);
    
    if (!retrievedNote) {
      throw new Error('Note was not persisted properly');
    }
    
    if (retrievedNote.noteText !== noteData.noteText) {
      throw new Error('Note content was not persisted correctly');
    }
    
    console.log('✅ Data persistence test passed');
    
    // Clean up
    await noteRepository.deleteNote(noteId);
  }
}

// Export a singleton instance for easy use
export const notesFeatureTester = new NotesFeatureTester();

// Helper function to run tests from console or debug panel
export const testNotesFeature = async (): Promise<void> => {
  try {
    await notesFeatureTester.runAllTests();
    await notesFeatureTester.testDataPersistence();
  } catch (error) {
    console.error('Notes feature test failed:', error);
    throw error;
  }
};
