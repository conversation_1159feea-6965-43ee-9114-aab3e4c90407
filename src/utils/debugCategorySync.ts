// Debug utility for category sync issues
import categoryApiService from '../services/categoryApiService';
import watermelonCategoryRepository from '../database/watermelon/repositories/categoryRepository';
import backgroundSyncManager from '../services/backgroundSyncManager';

export class CategorySyncDebugger {
  
  async debugCategorySync() {
    console.log('\n🔍 === CATEGORY SYNC DEBUG SESSION ===');
    
    try {
      // 1. Check API response
      console.log('\n📡 Step 1: Fetching categories from API...');
      const apiCategories = await categoryApiService.fetchCategories();
      console.log(`✅ API returned ${apiCategories.length} categories`);
      
      if (apiCategories.length > 0) {
        console.log('📋 Sample API category:', JSON.stringify(apiCategories[0], null, 2));
      }
      
      // 2. Check current database state
      console.log('\n💾 Step 2: Checking current database state...');
      const dbCategories = await watermelonCategoryRepository.getAll();
      console.log(`📊 Database has ${dbCategories.length} categories`);
      
      if (dbCategories.length > 0) {
        console.log('📋 Sample DB category:', JSON.stringify(dbCategories[0], null, 2));
        
        // Check for duplicates
        const names = dbCategories.map(cat => cat.name);
        const uniqueNames = [...new Set(names)];
        if (names.length !== uniqueNames.length) {
          console.warn(`⚠️  DUPLICATES DETECTED: ${names.length} total vs ${uniqueNames.length} unique`);
        }
        
        // Check for missing apiCategoryId
        const missingApiId = dbCategories.filter(cat => !cat.apiCategoryId || cat.apiCategoryId === 0);
        if (missingApiId.length > 0) {
          console.warn(`⚠️  ${missingApiId.length} categories have missing/zero apiCategoryId`);
        }
      }
      
      // 3. Test data mapping
      console.log('\n🔄 Step 3: Testing data mapping...');
      const mappedData = apiCategories.map(apiCategory => ({
        apiCategoryId: apiCategory.categoryId,
        name: apiCategory.name,
        iconUrl: apiCategory.iconUrl,
        isActive: apiCategory.isActive === true ? 1 : 0,
      }));
      
      console.log('📋 Sample mapped data:', JSON.stringify(mappedData[0], null, 2));
      
      return {
        apiCount: apiCategories.length,
        dbCount: dbCategories.length,
        apiSample: apiCategories[0],
        dbSample: dbCategories[0],
        mappedSample: mappedData[0],
      };
      
    } catch (error) {
      console.error('❌ Debug session failed:', error);
      throw error;
    }
  }
  
  async forceResetAndSync() {
    console.log('\n🔄 === FORCE RESET AND SYNC ===');
    
    try {
      // 1. Force reset categories
      console.log('\n🗑️  Step 1: Force resetting categories...');
      await backgroundSyncManager.forceResetCategories();
      
      // 2. Verify reset
      console.log('\n✅ Step 2: Verifying reset...');
      const afterReset = await watermelonCategoryRepository.getAll();
      console.log(`📊 Categories after reset: ${afterReset.length}`);
      
      // 3. Force sync
      console.log('\n🔄 Step 3: Force syncing categories...');
      await backgroundSyncManager.executeSync('categories');
      
      // 4. Verify sync
      console.log('\n✅ Step 4: Verifying sync...');
      const afterSync = await watermelonCategoryRepository.getAll();
      console.log(`📊 Categories after sync: ${afterSync.length}`);
      
      if (afterSync.length > 0) {
        console.log('📋 Sample synced category:', JSON.stringify(afterSync[0], null, 2));
      }
      
      return {
        beforeReset: 'unknown',
        afterReset: afterReset.length,
        afterSync: afterSync.length,
        sample: afterSync[0],
      };
      
    } catch (error) {
      console.error('❌ Force reset and sync failed:', error);
      throw error;
    }
  }
  
  async checkForDuplicates() {
    console.log('\n🔍 === CHECKING FOR DUPLICATES ===');
    
    try {
      const categories = await watermelonCategoryRepository.getAll();
      console.log(`📊 Total categories: ${categories.length}`);
      
      // Group by name
      const groupedByName = categories.reduce((acc, cat) => {
        if (!acc[cat.name]) {
          acc[cat.name] = [];
        }
        acc[cat.name].push(cat);
        return acc;
      }, {} as Record<string, any[]>);
      
      // Find duplicates
      const duplicates = Object.entries(groupedByName).filter(([name, cats]) => cats.length > 1);
      
      if (duplicates.length > 0) {
        console.warn(`⚠️  Found ${duplicates.length} duplicate category names:`);
        duplicates.forEach(([name, cats]) => {
          console.log(`   - "${name}": ${cats.length} entries`);
          cats.forEach(cat => {
            console.log(`     * ID: ${cat.categoryId}, API ID: ${cat.apiCategoryId}`);
          });
        });
      } else {
        console.log('✅ No duplicates found');
      }
      
      return duplicates;
      
    } catch (error) {
      console.error('❌ Duplicate check failed:', error);
      throw error;
    }
  }
}

export default new CategorySyncDebugger();
