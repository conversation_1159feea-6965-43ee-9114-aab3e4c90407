/**
 * Database Reset Utility
 * 
 * This utility helps reset the database when there are schema mismatches
 * or migration issues that prevent the app from working properly.
 */

import database from '../database/watermelon/database';
import backgroundSyncManager from '../services/backgroundSyncManager';

export class DatabaseResetUtility {
  
  /**
   * Reset the entire database and reinitialize with fresh data
   */
  static async resetDatabase() {
    console.log('\n=== Database Reset Started ===\n');
    
    try {
      // Step 1: Clear all data from all tables
      console.log('1. Clearing all data from database...');
      await database.write(async () => {
        await database.unsafeResetDatabase();
      });
      console.log('✅ Database cleared successfully');

      // Step 2: Wait a moment for the database to stabilize
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Step 3: Initialize background sync to populate fresh data
      console.log('2. Initializing background sync...');
      await backgroundSyncManager.initialize();
      console.log('✅ Background sync initialized');

      // Step 4: Start fresh sync
      console.log('3. Starting fresh data sync...');
      await backgroundSyncManager.startBackgroundSync();
      console.log('✅ Fresh sync started');

      console.log('\n=== Database Reset Completed ===\n');
      console.log('The database has been reset and fresh data sync has started.');
      console.log('Please wait for the sync to complete before using the app.');

    } catch (error) {
      console.error('❌ Error during database reset:', error);
      throw error;
    }
  }

  /**
   * Reset only company data (useful when company schema changes)
   */
  static async resetCompanyData() {
    console.log('\n=== Company Data Reset Started ===\n');
    
    try {
      // Step 1: Clear company and related data
      console.log('1. Clearing company data...');
      await database.write(async () => {
        // Clear companies
        const companies = await database.get('companies').query().fetch();
        const companyBatch = companies.map(company => company.prepareDestroyPermanently());
        
        // Clear company categories
        const companyCategories = await database.get('company_categories').query().fetch();
        const companyCategoryBatch = companyCategories.map(cc => cc.prepareDestroyPermanently());
        
        // Clear history
        const history = await database.get('history').query().fetch();
        const historyBatch = history.map(h => h.prepareDestroyPermanently());
        
        // Execute all deletions
        await database.batch([...companyBatch, ...companyCategoryBatch, ...historyBatch]);
      });
      console.log('✅ Company data cleared');

      // Step 2: Reset sync states for companies and company_categories
      console.log('2. Resetting sync states...');
      const syncStates = await database.get('sync_state').query().fetch();
      await database.write(async () => {
        const batch = syncStates
          .filter(state => state.syncKey === 'companies' || state.syncKey === 'company_categories')
          .map(state => state.prepareUpdate(s => {
            s.status = 'pending';
            s.progress = 0;
            s.retryCount = 0;
            s.errorMessage = undefined;
            s.lastSyncAt = undefined;
          }));
        
        if (batch.length > 0) {
          await database.batch(batch);
        }
      });
      console.log('✅ Sync states reset');

      // Step 3: Force sync companies and company categories
      console.log('3. Starting fresh company sync...');
      await backgroundSyncManager.forceSyncCompanies();
      await backgroundSyncManager.forceSyncCompanyCategories();
      console.log('✅ Company sync started');

      console.log('\n=== Company Data Reset Completed ===\n');

    } catch (error) {
      console.error('❌ Error during company data reset:', error);
      throw error;
    }
  }

  /**
   * Check database schema integrity
   */
  static async checkDatabaseIntegrity() {
    console.log('\n=== Database Integrity Check ===\n');
    
    try {
      // Check if all tables exist and have expected structure
      const tables = ['categories', 'companies', 'company_categories', 'history', 'sync_state'];
      
      for (const tableName of tables) {
        try {
          const collection = database.get(tableName);
          const count = await collection.query().fetchCount();
          console.log(`✅ Table '${tableName}': ${count} records`);
        } catch (error) {
          console.log(`❌ Table '${tableName}': Error - ${error.message}`);
        }
      }

      // Test creating a simple record to check schema
      console.log('\n--- Schema Validation ---');
      try {
        await database.write(async () => {
          // Try to create a test company record
          const testCompany = await database.get('companies').create(company => {
            company.companyId = 999999;
            company.companyName = 'Test Company';
            company.parentCompany = '';
            company.companyEmail = '';
            company.companyLogoUrl = '';
            company.companyCountry = '';
            company.companyAddress = '';
            company.companyWebsite = '';
            company.number = ''; // Test the number field
            company.upvoteCount = 0;
            company.downvoteCount = 0;
          });
          
          // If successful, delete the test record
          await testCompany.destroyPermanently();
          console.log('✅ Company schema validation passed');
        });
      } catch (error) {
        console.log(`❌ Company schema validation failed: ${error.message}`);
        console.log('This indicates a schema mismatch that requires database reset.');
      }

      console.log('\n=== Integrity Check Completed ===\n');

    } catch (error) {
      console.error('❌ Error during integrity check:', error);
      throw error;
    }
  }

  /**
   * Get detailed database information
   */
  static async getDatabaseInfo() {
    console.log('\n=== Database Information ===\n');
    
    try {
      const tables = ['categories', 'companies', 'company_categories', 'history', 'sync_state'];
      
      for (const tableName of tables) {
        try {
          const collection = database.get(tableName);
          const records = await collection.query().fetch();
          
          console.log(`--- ${tableName.toUpperCase()} ---`);
          console.log(`Records: ${records.length}`);
          
          if (records.length > 0) {
            const sample = records[0];
            console.log('Sample record fields:', Object.keys(sample._raw));
            
            if (tableName === 'companies' && records.length > 0) {
              const company = records[0];
              console.log('Sample company data:');
              console.log(`  ID: ${company.id}`);
              console.log(`  Company ID: ${company.companyId || 'undefined'}`);
              console.log(`  Name: ${company.companyName || 'undefined'}`);
              console.log(`  Number: ${company.number || 'undefined'}`);
            }
          }
          console.log('');
        } catch (error) {
          console.log(`❌ Error accessing ${tableName}: ${error.message}\n`);
        }
      }

    } catch (error) {
      console.error('❌ Error getting database info:', error);
      throw error;
    }
  }
}

export default DatabaseResetUtility;
