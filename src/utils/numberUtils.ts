import { ApiNumber } from '../services/companyApiService';

/**
 * Extract the first available phone number from the new nested object structure
 * Priority: TOLL_FREE -> ALL_INDIA -> INTERNATIONAL
 * 
 * @param numberField - Can be string (old format) or object (new format)
 * @returns First available phone number as string, or null if none found
 */
export const extractFirstAvailableNumber = (
  numberField?: string | {
    TOLL_FREE?: ApiNumber[];
    ALL_INDIA?: ApiNumber[];
    INTERNATIONAL?: ApiNumber[];
  } | null
): { number: string; isWhatsapp: boolean } | null => {
  // Handle null/undefined
  if (!numberField) {
    return null;
  }

  // Handle old string format (backward compatibility)
  if (typeof numberField === 'string') {
    const trimmed = numberField.trim();
    return trimmed ? { number: trimmed, isWhatsapp: false } : null;
  }

  // Helper to extract first number from a category
  const extractFromCategory = (companyNumbers?: ApiNumber[]) => {
    if (companyNumbers && companyNumbers.length > 0) {
      const first = companyNumbers[0];
      if (first.number) {
        return {
          number: first.number.trim(),
          isWhatsapp: !!first.isWhatsapp,
        };
      }
    }
    return null;
  };

  // Priority 1: TOLL_FREE
  const tollFree = extractFromCategory(numberField.TOLL_FREE);
  if (tollFree) return tollFree;

  // Priority 2: ALL_INDIA
  const allIndia = extractFromCategory(numberField.ALL_INDIA);
  if (allIndia) return allIndia;

  // Priority 3: INTERNATIONAL
  const international = extractFromCategory(numberField.INTERNATIONAL);
  if (international) return international;

  return null;
};

/**
 * Extract the first available number from local database numbers
 * Priority: TOLL_FREE -> ALL_INDIA -> INTERNATIONAL
 * 
 * @param localNumbers - Grouped numbers from local database
 * @returns First available phone number as string, or null if none found
 */
export const extractFirstAvailableLocalNumber = (
  localNumbers: {
    TOLL_FREE: ApiNumber[];
    ALL_INDIA: ApiNumber[];
    INTERNATIONAL: ApiNumber[];
  }
): { number: string; isWhatsapp: boolean } | null => {
  // Helper to extract from a category
  const extractFromCategory = (category?: ApiNumber[]) => {
    if (category && category.length > 0) {
      const first = category[0];
      if (first.number) {
        return {
          number: first.number.trim(),
          isWhatsapp: !!first.isWhatsapp,
        };
      }
    }
    return null;
  };

  // Priority 1: TOLL_FREE
  const tollFree = extractFromCategory(localNumbers.TOLL_FREE);
  if (tollFree) return tollFree;

  // Priority 2: ALL_INDIA
  const allIndia = extractFromCategory(localNumbers.ALL_INDIA);
  if (allIndia) return allIndia;

  // Priority 3: INTERNATIONAL
  const international = extractFromCategory(localNumbers.INTERNATIONAL);
  if (international) return international;

  return null;
};

/**
 * Get the type of the first available number
 * Priority: TOLL_FREE -> ALL_INDIA -> INTERNATIONAL
 * 
 * @param numberField - Can be string (old format) or object (new format)
 * @returns Type of the first available number, or null if none found
 */
export const getFirstAvailableNumberType = (
  numberField?: string | {
    TOLL_FREE?: ApiNumber[];
    ALL_INDIA?: ApiNumber[];
    INTERNATIONAL?: ApiNumber[];
  } | null
): string | null => {
  // Handle null/undefined
  if (!numberField) {
    return null;
  }

  // Handle old string format (backward compatibility)
  if (typeof numberField === 'string') {
    return 'LEGACY'; // Indicate this is from old format
  }

  // Handle new object format
  if (typeof numberField === 'object') {
    // Priority 1: TOLL_FREE
    if (numberField.TOLL_FREE && numberField.TOLL_FREE.length > 0) {
      return 'TOLL_FREE';
    }

    // Priority 2: ALL_INDIA
    if (numberField.ALL_INDIA && numberField.ALL_INDIA.length > 0) {
      return 'ALL_INDIA';
    }

    // Priority 3: INTERNATIONAL
    if (numberField.INTERNATIONAL && numberField.INTERNATIONAL.length > 0) {
      return 'INTERNATIONAL';
    }
  }

  return null;
};
