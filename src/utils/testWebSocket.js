// Test utility to verify WebSocket connection
// You can run this in the React Native debugger console to test the connection

import WebSocketVoteService from '../socket/iccappush.js';

export const testWebSocketConnection = async () => {
  console.log('🧪 Testing WebSocket connection...');
  
  try {
    // Test connection
    console.log('1. Testing connection...');
    await WebSocketVoteService.connect();
    console.log('✅ Connection test passed');
    
    // Test upvote
    console.log('2. Testing upvote...');
    await WebSocketVoteService.sendVote('1234567890', true);
    console.log('✅ Upvote test completed');
    
    // Test downvote
    console.log('3. Testing downvote...');
    await WebSocketVoteService.sendVote('1234567890', false);
    console.log('✅ Downvote test completed');
    
    console.log('🎉 All WebSocket tests passed!');
    
  } catch (error) {
    console.error('❌ WebSocket test failed:', error);
    console.log('This is expected if the server is not reachable');
  }
};

// Make it available globally for debugging
if (typeof global !== 'undefined') {
  global.testWebSocketConnection = testWebSocketConnection;
}

export default testWebSocketConnection;
