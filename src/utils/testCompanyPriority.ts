/**
 * Test utility to verify company priority functionality
 */

import watermelonCompanyRepository from '../database/watermelon/repositories/companyRepository';
import { convertDBCompaniesToUI } from './companyConverter';

export const testCompanyPriority = async () => {
  try {
    console.log('[CompanyPriorityTest] Starting company priority test...');

    // Test 1: Create test companies with different priorities
    const testCompanies = [
      {
        company_id: 99991,
        company_name: 'High Priority Company',
        company_priority: 1,
        parent_company: 'Test Parent 1',
        company_email: '<EMAIL>',
        company_logo_url: '',
        company_country: 'India',
        company_address: 'Test Address 1',
        company_website: 'https://test1.com',
        number: '1234567890',
        is_whatsapp: 0,
        upvote_count: 0,
        downvote_count: 0,
      },
      {
        company_id: 99992,
        company_name: 'Medium Priority Company',
        company_priority: 500,
        parent_company: 'Test Parent 2',
        company_email: '<EMAIL>',
        company_logo_url: '',
        company_country: 'India',
        company_address: 'Test Address 2',
        company_website: 'https://test2.com',
        number: '1234567891',
        is_whatsapp: 1,
        upvote_count: 0,
        downvote_count: 0,
      },
      {
        company_id: 99993,
        company_name: 'Low Priority Company',
        company_priority: 999999,
        parent_company: 'Test Parent 3',
        company_email: '<EMAIL>',
        company_logo_url: '',
        company_country: 'India',
        company_address: 'Test Address 3',
        company_website: 'https://test3.com',
        number: '1234567892',
        is_whatsapp: 0,
        upvote_count: 0,
        downvote_count: 0,
      },
      {
        company_id: 99994,
        company_name: 'Another High Priority Company',
        company_priority: 1,
        parent_company: 'Test Parent 4',
        company_email: '<EMAIL>',
        company_logo_url: '',
        company_country: 'India',
        company_address: 'Test Address 4',
        company_website: 'https://test4.com',
        number: '1234567893',
        is_whatsapp: 0,
        upvote_count: 0,
        downvote_count: 0,
      },
    ];

    // Create test companies
    console.log('[CompanyPriorityTest] Creating test companies...');
    for (const company of testCompanies) {
      await watermelonCompanyRepository.createOrUpdateByCompanyId(company);
    }

    // Test 2: Fetch companies using the new sorted method
    console.log('[CompanyPriorityTest] Fetching companies sorted by priority...');
    const sortedCompanies = await watermelonCompanyRepository.getAllSortedByPriority();
    
    // Filter only our test companies
    const testResults = sortedCompanies.filter(company => 
      company.company_id && company.company_id >= 99991 && company.company_id <= 99994
    );

    console.log('[CompanyPriorityTest] Test results:');
    testResults.forEach((company, index) => {
      console.log(`${index + 1}. ${company.company_name} - Priority: ${company.company_priority}`);
    });

    // Test 3: Verify sorting is correct
    let sortingCorrect = true;
    for (let i = 0; i < testResults.length - 1; i++) {
      const current = testResults[i];
      const next = testResults[i + 1];
      
      const currentPriority = current.company_priority || 999999;
      const nextPriority = next.company_priority || 999999;
      
      if (currentPriority > nextPriority) {
        sortingCorrect = false;
        console.error(`[CompanyPriorityTest] ❌ Sorting error: ${current.company_name} (${currentPriority}) should come after ${next.company_name} (${nextPriority})`);
      } else if (currentPriority === nextPriority) {
        // Check alphabetical order for same priority
        if (current.company_name > next.company_name) {
          sortingCorrect = false;
          console.error(`[CompanyPriorityTest] ❌ Alphabetical sorting error: ${current.company_name} should come after ${next.company_name} for same priority`);
        }
      }
    }

    // Test 4: Test UI conversion
    console.log('[CompanyPriorityTest] Testing UI conversion...');
    const uiCompanies = convertDBCompaniesToUI(testResults);
    console.log(`[CompanyPriorityTest] Converted ${uiCompanies.length} companies to UI format`);
    
    // Verify companyPriority is preserved in UI format
    uiCompanies.forEach(company => {
      if (company.companyPriority === undefined) {
        console.error(`[CompanyPriorityTest] ❌ companyPriority missing in UI format for ${company.companyName}`);
        sortingCorrect = false;
      }
    });

    // Clean up test data
    console.log('[CompanyPriorityTest] Cleaning up test data...');
    for (const company of testResults) {
      if (company.id) {
        await watermelonCompanyRepository.delete(company.id);
      }
    }

    if (sortingCorrect) {
      console.log('[CompanyPriorityTest] ✅ All tests passed! Company priority sorting is working correctly.');
    } else {
      console.error('[CompanyPriorityTest] ❌ Some tests failed. Please check the implementation.');
    }

    return sortingCorrect;

  } catch (error) {
    console.error('[CompanyPriorityTest] ❌ Test failed with error:', error);
    return false;
  }
};

export default testCompanyPriority;
