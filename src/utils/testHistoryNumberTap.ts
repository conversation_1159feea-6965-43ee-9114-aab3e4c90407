/**
 * Test utility for verifying number tap history functionality
 */

import historyRepository from '../database/watermelon/repositories/historyRepository';

export class HistoryNumberTapTester {
  /**
   * Test adding a number tap to history
   */
  static async testAddNumberTap(): Promise<void> {
    try {
      console.log('🧪 Testing number tap history functionality...');
      
      // Test data
      const testCompanyName = 'Test Company';
      const testNumber = '+91-1234567890';
      
      // Add number tap to history
      console.log(`Adding number tap: ${testCompanyName} - ${testNumber}`);
      const historyId = await historyRepository.addNumberTapToHistory(testCompanyName, testNumber, 12345, 67890);
      console.log(`✅ Number tap added to history with ID: ${historyId}`);

      // Verify it was added
      const numberTapEntries = await historyRepository.getNumberTapHistory();
      console.log(`📊 Total number tap entries in history: ${numberTapEntries.length}`);
      
      // Show the latest entry
      if (numberTapEntries.length > 0) {
        const latestEntry = numberTapEntries[0]; // getNumberTapHistory returns sorted by viewed_at desc
        console.log('📱 Latest number tap entry:', {
          company_name: latestEntry.company_name,
          number: latestEntry.number,
          viewed_at: latestEntry.viewed_at,
        });
      }
      
      // Test duplicate prevention
      console.log('\n🔄 Testing duplicate prevention...');
      const duplicateId = await historyRepository.addNumberTapToHistory(testCompanyName, testNumber, 12345, 67890);
      console.log(`Duplicate attempt result: ${duplicateId}`);

      // Check that we still have the same number of entries
      const finalHistory = await historyRepository.getNumberTapHistory();
      console.log(`📊 Final number tap entries count: ${finalHistory.length}`);

      console.log('✅ Number tap history test completed successfully!');
    } catch (error) {
      console.error('❌ Number tap history test failed:', error);
      throw error;
    }
  }
  
  /**
   * Test retrieving number tap history
   */
  static async testGetNumberTapHistory(): Promise<void> {
    try {
      console.log('🧪 Testing number tap history retrieval...');

      const numberTapHistory = await historyRepository.getNumberTapHistory();
      console.log(`📊 Total number tap entries: ${numberTapHistory.length}`);

      // Show sample entries
      if (numberTapHistory.length > 0) {
        console.log('📱 Sample number tap entry:', {
          company_name: numberTapHistory[0].company_name,
          number: numberTapHistory[0].number,
          viewed_at: numberTapHistory[0].viewed_at,
        });
      }

      console.log('✅ Number tap history retrieval test completed successfully!');
    } catch (error) {
      console.error('❌ Number tap history retrieval test failed:', error);
      throw error;
    }
  }
  
  /**
   * Run all history tests
   */
  static async runAllTests(): Promise<void> {
    try {
      console.log('🚀 Starting all history number tap tests...\n');
      
      await this.testAddNumberTap();
      console.log('');
      await this.testGetNumberTapHistory();
      
      console.log('\n🎉 All history number tap tests completed successfully!');
    } catch (error) {
      console.error('\n💥 History number tap tests failed:', error);
      throw error;
    }
  }
}

export default HistoryNumberTapTester;
