/**
 * Test utility for background sync functionality
 * This file can be used to manually test the background sync implementation
 */

import backgroundSyncManager from '../services/backgroundSyncManager';
import syncStateRepository from '../database/watermelon/repositories/syncStateRepository';

export class BackgroundSyncTester {

  /**
   * Test the complete sync flow
   */
  static async testFullSync() {
    console.log('[Test] Starting full sync test...');

    try {
      // Initialize the sync manager
      await backgroundSyncManager.initialize();
      console.log('[Test] ✅ Sync manager initialized');

      // Check initial sync states
      const categoriesState = await backgroundSyncManager.getSyncStatus('categories');
      const companiesState = await backgroundSyncManager.getSyncStatus('companies');

      console.log('[Test] Initial sync states:');
      console.log('  Categories:', categoriesState?.status, categoriesState?.progress + '%');
      console.log('  Companies:', companiesState?.status, companiesState?.progress + '%');

      // Start background sync
      await backgroundSyncManager.startBackgroundSync();
      console.log('[Test] ✅ Background sync started');

      // Monitor progress for 5 minutes (companies sync may take longer due to pagination)
      await this.monitorSyncProgress(300000);

    } catch (error) {
      console.error('[Test] ❌ Sync test failed:', error);
    }
  }

  /**
   * Monitor sync progress for a specified duration
   */
  static async monitorSyncProgress(durationMs: number) {
    console.log(`[Test] Monitoring sync progress for ${durationMs/1000} seconds...`);
    console.log('[Test] Note: Company sync may take longer due to pagination (~13k records)');

    const startTime = Date.now();
    let lastCategoriesProgress = -1;
    let lastCompaniesProgress = -1;

    const interval = setInterval(async () => {
      try {
        const categoriesState = await backgroundSyncManager.getSyncStatus('categories');
        const companiesState = await backgroundSyncManager.getSyncStatus('companies');

        // Only log if progress changed to reduce noise
        if (categoriesState?.progress !== lastCategoriesProgress ||
            companiesState?.progress !== lastCompaniesProgress) {

          console.log('[Test] Progress update:');
          console.log(`  Categories: ${categoriesState?.status} (${categoriesState?.progress}%)`);
          console.log(`  Companies: ${companiesState?.status} (${companiesState?.progress}%)`);

          lastCategoriesProgress = categoriesState?.progress || -1;
          lastCompaniesProgress = companiesState?.progress || -1;
        }

        // Check if both syncs are completed
        if (categoriesState?.status === 'completed' && companiesState?.status === 'completed') {
          console.log('[Test] ✅ All syncs completed!');
          console.log(`[Test] Total time: ${Math.round((Date.now() - startTime) / 1000)} seconds`);
          clearInterval(interval);
          return;
        }

        // Check if any sync failed
        if (categoriesState?.status === 'failed' || companiesState?.status === 'failed') {
          console.log('[Test] ❌ Some syncs failed');
          console.log('  Categories error:', categoriesState?.errorMessage);
          console.log('  Companies error:', companiesState?.errorMessage);
          clearInterval(interval);
          return;
        }

        // Check timeout
        if (Date.now() - startTime > durationMs) {
          console.log('[Test] ⏰ Monitoring timeout reached');
          console.log(`[Test] Final status after ${durationMs/1000}s:`);
          console.log(`  Categories: ${categoriesState?.status} (${categoriesState?.progress}%)`);
          console.log(`  Companies: ${companiesState?.status} (${companiesState?.progress}%)`);
          clearInterval(interval);
          return;
        }

      } catch (error) {
        console.error('[Test] Error monitoring progress:', error);
        clearInterval(interval);
      }
    }, 3000); // Check every 3 seconds (reduced frequency)
  }

  /**
   * Test sync resume functionality
   */
  static async testSyncResume() {
    console.log('[Test] Testing sync resume...');

    try {
      // Reset syncs to pending state
      await backgroundSyncManager.resetSync('categories');
      await backgroundSyncManager.resetSync('companies');
      console.log('[Test] ✅ Syncs reset to pending');

      // Initialize and check if syncs resume
      await backgroundSyncManager.initialize();
      console.log('[Test] ✅ Sync manager re-initialized');

      // Monitor for resume
      await this.monitorSyncProgress(15000);

    } catch (error) {
      console.error('[Test] ❌ Resume test failed:', error);
    }
  }

  /**
   * Test individual sync operations
   */
  static async testIndividualSync(syncKey: 'categories' | 'companies') {
    console.log(`[Test] Testing ${syncKey} sync...`);

    try {
      // Reset specific sync
      await backgroundSyncManager.resetSync(syncKey);
      console.log(`[Test] ✅ ${syncKey} sync reset`);

      // Get initial state
      const initialState = await backgroundSyncManager.getSyncStatus(syncKey);
      console.log(`[Test] Initial state: ${initialState?.status}`);

      // Initialize sync manager
      await backgroundSyncManager.initialize();

      // Monitor this specific sync
      const startTime = Date.now();
      const interval = setInterval(async () => {
        const state = await backgroundSyncManager.getSyncStatus(syncKey);
        console.log(`[Test] ${syncKey}: ${state?.status} (${state?.progress}%)`);

        if (state?.status === 'completed') {
          console.log(`[Test] ✅ ${syncKey} sync completed!`);
          clearInterval(interval);
        } else if (state?.status === 'failed') {
          console.log(`[Test] ❌ ${syncKey} sync failed: ${state.errorMessage}`);
          clearInterval(interval);
        } else if (Date.now() - startTime > 20000) {
          console.log(`[Test] ⏰ ${syncKey} sync timeout`);
          clearInterval(interval);
        }
      }, 1000);

    } catch (error) {
      console.error(`[Test] ❌ ${syncKey} sync test failed:`, error);
    }
  }

  /**
   * Get detailed sync status report
   */
  static async getSyncReport() {
    console.log('[Test] Generating sync status report...');

    try {
      const categoriesState = await backgroundSyncManager.getSyncStatus('categories');
      const companiesState = await backgroundSyncManager.getSyncStatus('companies');

      const report = {
        timestamp: new Date().toISOString(),
        categories: {
          status: categoriesState?.status || 'unknown',
          progress: categoriesState?.progress || 0,
          lastSync: categoriesState?.lastSyncAt?.toISOString() || 'never',
          retryCount: categoriesState?.retryCount || 0,
          error: categoriesState?.errorMessage || null,
        },
        companies: {
          status: companiesState?.status || 'unknown',
          progress: companiesState?.progress || 0,
          lastSync: companiesState?.lastSyncAt?.toISOString() || 'never',
          retryCount: companiesState?.retryCount || 0,
          error: companiesState?.errorMessage || null,
        }
      };

      console.log('[Test] Sync Status Report:');
      console.log(JSON.stringify(report, null, 2));

      return report;

    } catch (error) {
      console.error('[Test] ❌ Failed to generate report:', error);
      return null;
    }
  }

  /**
   * Clean up all sync states (for testing)
   */
  static async cleanupSyncStates() {
    console.log('[Test] Cleaning up sync states...');

    try {
      const pendingSyncs = await syncStateRepository.getPendingOrFailedSyncs();

      for (const sync of pendingSyncs) {
        if (sync.id) {
          await syncStateRepository.delete(sync.id);
        }
      }

      console.log(`[Test] ✅ Cleaned up ${pendingSyncs.length} sync states`);

    } catch (error) {
      console.error('[Test] ❌ Cleanup failed:', error);
    }
  }
}

// Export convenience functions for console testing
export const testFullSync = () => BackgroundSyncTester.testFullSync();
export const testSyncResume = () => BackgroundSyncTester.testSyncResume();
export const testCategoriesSync = () => BackgroundSyncTester.testIndividualSync('categories');
export const testCompaniesSync = () => BackgroundSyncTester.testIndividualSync('companies');
export const getSyncReport = () => BackgroundSyncTester.getSyncReport();
export const cleanupSyncStates = () => BackgroundSyncTester.cleanupSyncStates();
