/**
 * Utility to force a fresh company sync for testing company priority
 */

import backgroundSyncManager from '../services/backgroundSyncManager';
import syncStateRepository from '../database/watermelon/repositories/syncStateRepository';
import watermelonCompanyRepository from '../database/watermelon/repositories/companyRepository';

export const forceCompanySync = async () => {
  try {
    console.log('[ForceSync] Starting forced company sync...');

    // Step 1: Reset the company sync state to force a fresh sync
    console.log('[ForceSync] Resetting company sync state...');
    await syncStateRepository.createOrUpdate({
      syncKey: 'companies',
      status: 'pending',
      progress: 0,
      lastDataTime: 0, // Reset to 0 to get all companies
      retryCount: 0,
    });

    // Step 2: Check current company count before sync
    const beforeCount = await watermelonCompanyRepository.getCount();
    console.log(`[ForceSync] Companies in DB before sync: ${beforeCount}`);

    // Step 3: Start the sync
    console.log('[ForceSync] Starting background sync...');
    await backgroundSyncManager.startBackgroundSync();

    // Step 4: Wait a bit for sync to start
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Step 5: Monitor sync progress
    let attempts = 0;
    const maxAttempts = 30; // Wait up to 60 seconds
    
    while (attempts < maxAttempts) {
      const syncState = await syncStateRepository.getBySyncKey('companies');
      console.log(`[ForceSync] Sync status: ${syncState?.status}, Progress: ${syncState?.progress}%`);
      
      if (syncState?.status === 'completed') {
        console.log('[ForceSync] ✅ Sync completed successfully!');
        break;
      } else if (syncState?.status === 'failed') {
        console.log('[ForceSync] ❌ Sync failed!');
        break;
      }
      
      await new Promise(resolve => setTimeout(resolve, 2000));
      attempts++;
    }

    // Step 6: Check results
    const afterCount = await watermelonCompanyRepository.getCount();
    console.log(`[ForceSync] Companies in DB after sync: ${afterCount}`);

    // Step 7: Test company priority data
    console.log('[ForceSync] Testing company priority data...');
    const companies = await watermelonCompanyRepository.getAll();
    const first5 = companies.slice(0, 5);
    
    console.log('[ForceSync] First 5 companies with priority:');
    first5.forEach((company, index) => {
      console.log(`${index + 1}. ${company.company_name} - Priority: ${company.company_priority}`);
    });

    // Step 8: Test sorted method
    console.log('[ForceSync] Testing sorted method...');
    const sortedCompanies = await watermelonCompanyRepository.getAllSortedByPriority();
    const first5Sorted = sortedCompanies.slice(0, 5);
    
    console.log('[ForceSync] First 5 sorted companies:');
    first5Sorted.forEach((company, index) => {
      console.log(`${index + 1}. ${company.company_name} - Priority: ${company.company_priority}`);
    });

    console.log('[ForceSync] Force sync test completed!');

  } catch (error) {
    console.error('[ForceSync] Error during forced sync:', error);
  }
};

export const quickCompanyPriorityCheck = async () => {
  try {
    console.log('[QuickCheck] Checking company priority in database...');
    
    const companies = await watermelonCompanyRepository.getAll();
    console.log(`[QuickCheck] Total companies: ${companies.length}`);
    
    const companiesWithPriority = companies.filter(c => c.company_priority !== undefined && c.company_priority !== null);
    const companiesWithoutPriority = companies.filter(c => c.company_priority === undefined || c.company_priority === null);
    
    console.log(`[QuickCheck] Companies with priority: ${companiesWithPriority.length}`);
    console.log(`[QuickCheck] Companies without priority: ${companiesWithoutPriority.length}`);
    
    if (companiesWithPriority.length > 0) {
      console.log('[QuickCheck] Sample companies with priority:');
      companiesWithPriority.slice(0, 3).forEach((company, index) => {
        console.log(`${index + 1}. ${company.company_name} - Priority: ${company.company_priority}`);
      });
    }
    
    if (companiesWithoutPriority.length > 0) {
      console.log('[QuickCheck] Sample companies without priority:');
      companiesWithoutPriority.slice(0, 3).forEach((company, index) => {
        console.log(`${index + 1}. ${company.company_name} - Priority: ${company.company_priority}`);
      });
    }

  } catch (error) {
    console.error('[QuickCheck] Error during quick check:', error);
  }
};

export default forceCompanySync;
