import backgroundSyncManager from '../services/backgroundSyncManager';
import syncStateRepository from '../database/watermelon/repositories/syncStateRepository';

/**
 * Debug utility functions for sync management
 */
export class DebugSync {
  /**
   * Get detailed sync status for all sync keys
   */
  static async getAllSyncStatus() {
    console.log('[DebugSync] Getting all sync statuses...');
    
    const syncKeys = ['categories', 'companies', 'company_categories'];
    const statuses = [];
    
    for (const syncKey of syncKeys) {
      try {
        const syncState = await syncStateRepository.getBySyncKey(syncKey);
        if (syncState) {
          const timeSinceUpdate = Date.now() - (syncState.updatedAt?.getTime() || 0);
          const minutesSinceUpdate = Math.floor(timeSinceUpdate / (1000 * 60));
          
          statuses.push({
            syncKey,
            status: syncState.status,
            progress: syncState.progress,
            retryCount: syncState.retryCount,
            errorMessage: syncState.errorMessage,
            minutesSinceUpdate,
            lastSyncAt: syncState.lastSyncAt,
          });
          
          console.log(`[DebugSync] ${syncKey}: ${syncState.status} (${syncState.progress}%) - Updated ${minutesSinceUpdate} minutes ago`);
          if (syncState.errorMessage) {
            console.log(`[DebugSync] ${syncKey} Error: ${syncState.errorMessage}`);
          }
        } else {
          console.log(`[DebugSync] ${syncKey}: No sync state found`);
          statuses.push({
            syncKey,
            status: 'not_found',
            progress: 0,
            retryCount: 0,
            errorMessage: null,
            minutesSinceUpdate: 0,
            lastSyncAt: null,
          });
        }
      } catch (error) {
        console.error(`[DebugSync] Error getting sync status for ${syncKey}:`, error);
      }
    }
    
    return statuses;
  }

  /**
   * Force restart a specific sync
   */
  static async forceRestartSync(syncKey: 'categories' | 'companies' | 'company_categories') {
    console.log(`[DebugSync] Force restarting ${syncKey} sync...`);
    
    try {
      await backgroundSyncManager.forceRestartStuckSync(syncKey);
      console.log(`[DebugSync] ✅ Successfully restarted ${syncKey} sync`);
    } catch (error) {
      console.error(`[DebugSync] ❌ Failed to restart ${syncKey} sync:`, error);
      throw error;
    }
  }

  /**
   * Force restart companies sync specifically
   */
  static async forceRestartCompaniesSync() {
    return this.forceRestartSync('companies');
  }

  /**
   * Check for stuck syncs and report them
   */
  static async checkStuckSyncs() {
    console.log('[DebugSync] Checking for stuck syncs...');
    
    const statuses = await this.getAllSyncStatus();
    const stuckSyncs = [];
    const stuckThresholdMinutes = 10;
    
    for (const status of statuses) {
      if (status.status === 'in_progress' && status.minutesSinceUpdate > stuckThresholdMinutes) {
        stuckSyncs.push(status);
        console.log(`[DebugSync] 🚨 STUCK SYNC DETECTED: ${status.syncKey} has been in progress for ${status.minutesSinceUpdate} minutes`);
      }
    }
    
    if (stuckSyncs.length === 0) {
      console.log('[DebugSync] ✅ No stuck syncs detected');
    }
    
    return stuckSyncs;
  }

  /**
   * Auto-fix stuck syncs
   */
  static async autoFixStuckSyncs() {
    console.log('[DebugSync] Auto-fixing stuck syncs...');
    
    const stuckSyncs = await this.checkStuckSyncs();
    
    for (const stuckSync of stuckSyncs) {
      console.log(`[DebugSync] Auto-fixing stuck sync: ${stuckSync.syncKey}`);
      try {
        await this.forceRestartSync(stuckSync.syncKey as any);
      } catch (error) {
        console.error(`[DebugSync] Failed to auto-fix ${stuckSync.syncKey}:`, error);
      }
    }
    
    return stuckSyncs.length;
  }
}

// Make it available globally for debugging in console
if (typeof global !== 'undefined') {
  (global as any).DebugSync = DebugSync;
}

export default DebugSync;
