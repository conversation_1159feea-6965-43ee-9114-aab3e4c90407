// NetworkStateManager.js
import NetInfo from '@react-native-community/netinfo';
import {useEffect, useState} from 'react';

// Custom hook for network state
export const useNetworkState = () => {
  const [isConnected, setIsConnected] = useState(true);

  useEffect(() => {
    // Subscribe to network state updates
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsConnected(state.isConnected);
    });

    // Initial check
    NetInfo.fetch().then(state => {
      setIsConnected(state.isConnected);
    });

    // Cleanup subscription on unmount
    return () => {
      unsubscribe();
    };
  }, []);

  return {isConnected};
};

// NetworkStateManager class (if you prefer class-based approach)
export class NetworkStateManager {
  constructor() {
    this.listeners = [];
    this.unsubscribe = null;
    this.isConnected = true;
  }

  initialize() {
    // Subscribe to network state changes
    this.unsubscribe = NetInfo.addEventListener(state => {
      const previousState = this.isConnected;
      this.isConnected = state.isConnected;

      // Notify listeners only if state actually changed
      if (previousState !== this.isConnected) {
        this.notifyListeners();
      }
    });

    // Initial check
    NetInfo.fetch().then(state => {
      this.isConnected = state.isConnected;
      this.notifyListeners();
    });
  }

  addListener(listener) {
    this.listeners.push(listener);

    // Initialize subscription if this is the first listener
    if (this.listeners.length === 1 && !this.unsubscribe) {
      this.initialize();
    }

    return () => this.removeListener(listener);
  }

  removeListener(listener) {
    this.listeners = this.listeners.filter(l => l !== listener);

    // Clean up subscription if there are no more listeners
    if (this.listeners.length === 0 && this.unsubscribe) {
      this.unsubscribe();
      this.unsubscribe = null;
    }
  }

  notifyListeners() {
    this.listeners.forEach(listener => {
      if (this.isConnected) {
        listener.onNetworkAvailable?.();
      } else {
        listener.onNetworkLost?.();
      }
    });
  }

  isNetworkAvailable() {
    return this.isConnected;
  }

  cleanup() {
    if (this.unsubscribe) {
      this.unsubscribe();
      this.unsubscribe = null;
    }
    this.listeners = [];
  }
}

// Singleton instance
export const networkStateManager = new NetworkStateManager();
