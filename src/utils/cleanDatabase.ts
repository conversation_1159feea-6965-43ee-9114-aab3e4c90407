// Database cleaning utility for fresh testing
import database from '../database/watermelon/database';
import watermelonCategoryRepository from '../database/watermelon/repositories/categoryRepository';
import watermelonCompanyRepository from '../database/watermelon/repositories/companyRepository';
import historyRepository from '../database/watermelon/repositories/historyRepository';
import syncStateRepository from '../database/watermelon/repositories/syncStateRepository';

export class DatabaseCleaner {

  async cleanAllData() {
    console.log('\n🧹 === CLEANING ALL DATABASE DATA ===');

    try {
      await database.write(async () => {
        // Get all collections
        const categoryCollection = database.get('categories');
        const companyCollection = database.get('companies');
        const historyCollection = database.get('history');
        const syncStateCollection = database.get('sync_state');

        // Get all records
        const [categories, companies, histories, syncStates] = await Promise.all([
          categoryCollection.query().fetch(),
          companyCollection.query().fetch(),
          historyCollection.query().fetch(),
          syncStateCollection.query().fetch(),
        ]);

        console.log(`📊 Found data to clean:`);
        console.log(`   - Categories: ${categories.length}`);
        console.log(`   - Companies: ${companies.length}`);
        console.log(`   - History: ${histories.length}`);
        console.log(`   - Sync States: ${syncStates.length}`);

        // Delete all records permanently
        const deletePromises = [
          ...categories.map(record => record.destroyPermanently()),
          ...companies.map(record => record.destroyPermanently()),
          ...histories.map(record => record.destroyPermanently()),
          ...syncStates.map(record => record.destroyPermanently()),
        ];

        await Promise.all(deletePromises);

        console.log('✅ All data cleaned successfully');
      });

      // Verify cleanup
      await this.verifyCleanup();

    } catch (error) {
      console.error('❌ Error cleaning database:', error);
      throw error;
    }
  }

  async verifyCleanup() {
    console.log('\n🔍 === VERIFYING CLEANUP ===');

    try {
      // Get all data using available methods
      const [categories, companies, histories] = await Promise.all([
        watermelonCategoryRepository.getAll(),
        watermelonCompanyRepository.getAll(),
        historyRepository.getAll(),
      ]);

      // Get sync states using direct collection query
      const syncStateCollection = database.get('sync_state');
      const syncStates = await syncStateCollection.query().fetch();

      console.log(`📊 After cleanup:`);
      console.log(`   - Categories: ${categories.length}`);
      console.log(`   - Companies: ${companies.length}`);
      console.log(`   - History: ${histories.length}`);
      console.log(`   - Sync States: ${syncStates.length}`);

      const totalRecords = categories.length + companies.length + histories.length + syncStates.length;

      if (totalRecords === 0) {
        console.log('✅ Database is completely clean');
        return true;
      } else {
        console.warn(`⚠️  ${totalRecords} records still remain`);
        return false;
      }

    } catch (error) {
      console.error('❌ Error verifying cleanup:', error);
      throw error;
    }
  }

  async cleanCategoriesOnly() {
    console.log('\n🧹 === CLEANING CATEGORIES ONLY ===');

    try {
      await database.write(async () => {
        const categoryCollection = database.get('categories');
        const categories = await categoryCollection.query().fetch();

        console.log(`📊 Found ${categories.length} categories to clean`);

        await Promise.all(categories.map(record => record.destroyPermanently()));

        console.log('✅ Categories cleaned successfully');
      });

      // Also clean category sync state
      const categorySyncState = await syncStateRepository.getBySyncKey('categories');
      if (categorySyncState && categorySyncState.id) {
        await syncStateRepository.delete(categorySyncState.id);
        console.log('✅ Category sync state cleaned');
      }

      // Verify
      const remainingCategories = await watermelonCategoryRepository.getAll();
      console.log(`📊 Categories remaining: ${remainingCategories.length}`);

    } catch (error) {
      console.error('❌ Error cleaning categories:', error);
      throw error;
    }
  }

  async resetToFreshState() {
    console.log('\n🔄 === RESETTING TO FRESH STATE ===');

    try {
      // 1. Clean all data
      await this.cleanAllData();

      // 2. Initialize sync states as pending
      await syncStateRepository.createOrUpdate({
        syncKey: 'categories',
        status: 'pending',
        progress: 0,
        retryCount: 0,
        errorMessage: undefined,
      });

      await syncStateRepository.createOrUpdate({
        syncKey: 'companies',
        status: 'pending',
        progress: 0,
        retryCount: 0,
        errorMessage: undefined,
      });

      console.log('✅ Database reset to fresh state');
      console.log('📝 Sync states initialized as pending');
      console.log('🚀 Ready for fresh testing!');

    } catch (error) {
      console.error('❌ Error resetting to fresh state:', error);
      throw error;
    }
  }

  async getCurrentState() {
    console.log('\n📊 === CURRENT DATABASE STATE ===');

    try {
      // Get all data using available methods
      const [categories, companies, histories] = await Promise.all([
        watermelonCategoryRepository.getAll(),
        watermelonCompanyRepository.getAll(),
        historyRepository.getAll(),
      ]);

      // Get sync states using direct collection query
      const syncStateCollection = database.get('sync_state');
      const syncStates = await syncStateCollection.query().fetch();

      console.log(`📊 Current data:`);
      console.log(`   - Categories: ${categories.length}`);
      console.log(`   - Companies: ${companies.length}`);
      console.log(`   - History: ${histories.length}`);
      console.log(`   - Sync States: ${syncStates.length}`);

      if (categories.length > 0) {
        console.log('📋 Sample category:', {
          id: categories[0].categoryId,
          apiCategoryId: categories[0].apiCategoryId,
          name: categories[0].name,
          isActive: categories[0].isActive,
        });
      }

      if (syncStates.length > 0) {
        console.log('📋 Sync states:');
        syncStates.forEach(state => {
          console.log(`   - ${state.syncKey}: ${state.status} (${state.progress}%)`);
        });
      }

      return {
        categories: categories.length,
        companies: companies.length,
        histories: histories.length,
        syncStates: syncStates.length,
        sampleCategory: categories[0] || null,
      };

    } catch (error) {
      console.error('❌ Error getting current state:', error);
      throw error;
    }
  }
}

export default new DatabaseCleaner();
