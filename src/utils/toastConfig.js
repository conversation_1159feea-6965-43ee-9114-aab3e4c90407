import React from 'react';
import {Text, View} from 'react-native';
import {BaseToast} from 'react-native-toast-message';
import {colors, fonts} from './theme';
import {Cancel, CheckCircle, Info, WarningCircle} from '../assets/images/svgs';
import {HORIZONTAL_DIMENSIONS, VERTICAL_DIMENSIONS} from '../constants/dimens';
import {IOS} from '../constants';
import {tr} from 'date-fns/locale';
import {err} from 'react-native-svg/lib/typescript/xml';
import {COLORS, FONTS} from '../common';

export default {
  success: props => {
    <BaseToast
      {...props}
      text1NumberOfLines={3}
      style={{
        borderRadius: 10,
        borderLeftColor: COLORS.WHITE,
        backgroundColor: COLORS.WHITE,
        paddingHorizontal: 20,
        alignItems: 'center',
        width: HORIZONTAL_DIMENSIONS._388,
        marginTop: IOS ? VERTICAL_DIMENSIONS._5 : 0,
      }}
      text1Style={{
        right: 10,
        fontFamily: FONTS.POPPINS.MEDIUM,
        fontWeight: '500',
        fontSize: 14,
        color: colors.black,
      }}
      renderLeadingIcon={() => <CheckCircle />}
    />;
  },
  warning: props => {
    return (
      <BaseToast
        {...props}
        text1NumberOfLines={3}
        style={{
          borderRadius: 10,
          borderLeftColor: COLORS.WHITE,
          backgroundColor: COLORS.WHITE,
          paddingHorizontal: 10,
          alignItems: 'center',
          width: HORIZONTAL_DIMENSIONS._388,
          marginTop: IOS ? VERTICAL_DIMENSIONS._5 : 0,
        }}
        text1Style={{
          right: 10,
          fontFamily: FONTS.POPPINS.MEDIUM,
          fontWeight: '500',
          fontSize: 14,
          color: colors.black,
        }}
        renderLeadingIcon={() => <WarningCircle />}
      />
    );
  },
  info1: props => {
    return (
      <BaseToast
        {...props}
        text1NumberOfLines={5}
        style={{
          borderRadius: 10,
          borderLeftColor: COLORS.WHITE,
          backgroundColor: COLORS.WHITE,
          paddingHorizontal: 10,
          alignItems: 'center',
          width: HORIZONTAL_DIMENSIONS._388,
          marginTop: IOS ? VERTICAL_DIMENSIONS._5 : 0,
        }}
        text1Style={{
          right: 10,
          fontFamily: FONTS.POPPINS.MEDIUM,
          fontWeight: '500',
          fontSize: 14,
          width: HORIZONTAL_DIMENSIONS._275,
          color: colors.black,
        }}
        renderLeadingIcon={() => <Info />}
      />
    );
  },
  info: props => {
    return (
      <BaseToast
        {...props}
        text1NumberOfLines={5}
        style={{
          borderRadius: 10,
          borderLeftColor: COLORS.WHITE,
          backgroundColor: COLORS.WHITE,
          paddingHorizontal: 10,
          alignItems: 'center',
          width: HORIZONTAL_DIMENSIONS._388,
          marginTop: IOS ? VERTICAL_DIMENSIONS._5 : 0,
        }}
        text1Style={{
          right: 10,
          fontFamily: FONTS.POPPINS.MEDIUM,
          fontWeight: '500',
          fontSize: 14,
          width: HORIZONTAL_DIMENSIONS._275,
          color: colors.black,
        }}
        renderLeadingIcon={() => <CheckCircle />}
      />
    );
  },
  error: props => {
    return (
      <BaseToast
        {...props}
        text1NumberOfLines={5}
        style={{
          borderRadius: 10,
          paddingHorizontal: 10,
          borderLeftColor: COLORS.WHITE,
          backgroundColor: COLORS.WHITE,
          alignItems: 'center',
          width: HORIZONTAL_DIMENSIONS._388,
          marginTop: IOS ? VERTICAL_DIMENSIONS._5 : 0,
        }}
        text1Style={{
          right: 10,
          fontFamily: FONTS.POPPINS.MEDIUM,
          fontWeight: '500',
          fontSize: 14,
          width: HORIZONTAL_DIMENSIONS._275,
          width: '100%',
          color: colors.black,
        }}
        renderLeadingIcon={() => <Cancel />}
      />
    );
  },
  tomatoToast: ({props, text1}) => {
    return (
      <View
        style={{
          flexDirection: 'row',
          borderRadius: 20,
          borderLeftColor: COLORS.WHITE,
          backgroundColor: COLORS.WHITE,
          paddingHorizontal: 20,
          paddingVertical: 12,
          alignItems: 'center',
          width: HORIZONTAL_DIMENSIONS._388,
          marginTop: IOS ? VERTICAL_DIMENSIONS._5 : 0,
        }}>
        {props ? <Cancel /> : <WarningCircle />}
        <Text
          style={{
            left: 10,
            fontFamily: FONTS.POPPINS.MEDIUM,
            fontWeight: '500',
            fontSize: 14,
            color: colors.black,
            width: '82%',
          }}
          numberOfLines={0}>
          {text1}
        </Text>
      </View>
    );
  },
};
