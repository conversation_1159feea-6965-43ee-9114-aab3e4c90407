import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useRef,
} from 'react';
import {
  initializeVoiceRecognition,
  startListening,
  stopListening,
  cleanupVoiceEventListeners,
} from '../services/VoiceRecognitionService';

interface VoiceRecognitionContextType {
  registerScreen: (
    screenName: string,
    onSpeechStart: () => void,
    onSpeechEnd: () => void,
    onSpeechResults: (results: string[]) => void,
    onSpeechError: (error: any) => void,
  ) => void;
  unregisterScreen: (screenName: string) => void;
  startVoiceRecognition: () => Promise<boolean>;
  stopVoiceRecognition: () => Promise<void>;
  isListening: boolean;
  activeScreen: string | null;
}

const VoiceRecognitionContext =
  createContext<VoiceRecognitionContextType | null>(null);

export const useVoiceRecognition = () => {
  const context = useContext(VoiceRecognitionContext);
  if (!context) {
    throw new Error(
      'useVoiceRecognition must be used within a VoiceRecognitionProvider',
    );
  }
  return context;
};

interface ScreenHandlers {
  onSpeechStart: () => void;
  onSpeechEnd: () => void;
  onSpeechResults: (results: string[]) => void;
  onSpeechError: (error: any) => void;
}

export const VoiceRecognitionProvider: React.FC<{
  children: React.ReactNode;
}> = ({children}) => {
  const [activeScreen, setActiveScreen] = useState<string | null>(null);
  const [isListening, setIsListening] = useState(false);
  const screenHandlers = useRef<Map<string, ScreenHandlers>>(new Map());
  const isInitialized = useRef(false);
  const activeScreenRef = useRef<string | null>(null);

  const registerScreen = useCallback(
    (
      screenName: string,
      onSpeechStart: () => void,
      onSpeechEnd: () => void,
      onSpeechResults: (results: string[]) => void,
      onSpeechError: (error: any) => void,
    ) => {
      console.log(`[VoiceManager] Registering screen: ${screenName}`);

      // Store the handlers for this screen
      screenHandlers.current.set(screenName, {
        onSpeechStart,
        onSpeechEnd,
        onSpeechResults,
        onSpeechError,
      });

      // Set this screen as active
      setActiveScreen(screenName);
      activeScreenRef.current = screenName;
      console.log(`[VoiceManager] Active screen set to: ${screenName}`);

      // Initialize voice recognition if not already done
      if (!isInitialized.current) {
        console.log(
          `[VoiceManager] Initializing voice recognition for ${screenName}`,
        );

        const globalHandlers = {
          onSpeechStart: () => {
            const currentActiveScreen = activeScreenRef.current;
            console.log(
              `[VoiceManager] Speech started - active screen: ${currentActiveScreen}`,
            );
            setIsListening(true);
            const currentHandlers = screenHandlers.current.get(
              currentActiveScreen || '',
            );
            if (currentHandlers) {
              currentHandlers.onSpeechStart();
            }
          },
          onSpeechEnd: () => {
            const currentActiveScreen = activeScreenRef.current;
            console.log(
              `[VoiceManager] Speech ended - active screen: ${currentActiveScreen}`,
            );
            setIsListening(false);
            const currentHandlers = screenHandlers.current.get(
              currentActiveScreen || '',
            );
            if (currentHandlers) {
              currentHandlers.onSpeechEnd();
            }
          },
          onSpeechResults: (results: string[]) => {
            const currentActiveScreen = activeScreenRef.current;
            console.log(
              `[VoiceManager] Speech results received for active screen: ${currentActiveScreen}`,
              results,
            );
            const currentHandlers = screenHandlers.current.get(
              currentActiveScreen || '',
            );
            if (currentHandlers) {
              currentHandlers.onSpeechResults(results);
            } else {
              console.warn(
                `[VoiceManager] No handlers found for active screen: ${currentActiveScreen}`,
              );
            }
          },
          onSpeechError: (error: any) => {
            const currentActiveScreen = activeScreenRef.current;
            console.log(
              `[VoiceManager] Speech error for active screen: ${currentActiveScreen}`,
              error,
            );
            setIsListening(false);
            const currentHandlers = screenHandlers.current.get(
              currentActiveScreen || '',
            );
            if (currentHandlers) {
              currentHandlers.onSpeechError(error);
            }
          },
        };

        initializeVoiceRecognition(
          globalHandlers.onSpeechStart,
          globalHandlers.onSpeechEnd,
          globalHandlers.onSpeechResults,
          globalHandlers.onSpeechError,
          'VoiceManager',
        );

        isInitialized.current = true;
      } else {
        console.log(
          `[VoiceManager] Voice recognition already initialized, screen ${screenName} registered`,
        );
      }
    },
    [activeScreen],
  );

  const unregisterScreen = useCallback(
    (screenName: string) => {
      console.log(`[VoiceManager] Unregistering screen: ${screenName}`);
      screenHandlers.current.delete(screenName);

      // If this was the active screen, clear it
      if (activeScreen === screenName) {
        setActiveScreen(null);
        activeScreenRef.current = null;
        console.log(`[VoiceManager] Active screen cleared`);
      }
    },
    [activeScreen],
  );

  const startVoiceRecognition = useCallback(async () => {
    if (!activeScreen) {
      console.warn(
        '[VoiceManager] No active screen to start voice recognition',
      );
      return false;
    }

    console.log(
      `[VoiceManager] Starting voice recognition for active screen: ${activeScreen}`,
    );
    return await startListening();
  }, [activeScreen]);

  const stopVoiceRecognition = useCallback(async () => {
    console.log(`[VoiceManager] Stopping voice recognition`);
    setIsListening(false);
    return await stopListening();
  }, []);

  const value: VoiceRecognitionContextType = {
    registerScreen,
    unregisterScreen,
    startVoiceRecognition,
    stopVoiceRecognition,
    isListening,
    activeScreen,
  };

  return (
    <VoiceRecognitionContext.Provider value={value}>
      {children}
    </VoiceRecognitionContext.Provider>
  );
};
