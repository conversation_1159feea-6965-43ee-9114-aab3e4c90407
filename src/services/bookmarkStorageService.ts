import AsyncStorage from '@react-native-async-storage/async-storage';

const STORAGE_KEYS = {
  BOOKMARKED_COMPANIES: 'bookmarked_companies',
  COMPANY_SHORTCUTS: 'company_shortcuts',
} as const;

export interface BookmarkedCompany {
  companyId: number;
  companyName: string;
  bookmarkedAt: string; // ISO date string
  hasShortcut: boolean;
  shortcutId?: string;
}

export interface CompanyShortcut {
  companyId: number;
  shortcutId: string;
  createdAt: string; // ISO date string
}

class BookmarkStorageService {
  /**
   * Get all bookmarked companies
   */
  async getBookmarkedCompanies(): Promise<BookmarkedCompany[]> {
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEYS.BOOKMARKED_COMPANIES);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('[BookmarkStorageService] Error getting bookmarked companies:', error);
      return [];
    }
  }

  /**
   * Check if a company is bookmarked
   */
  async isCompanyBookmarked(companyId: number): Promise<boolean> {
    try {
      const bookmarked = await this.getBookmarkedCompanies();
      return bookmarked.some(company => company.companyId === companyId);
    } catch (error) {
      console.error('[BookmarkStorageService] Error checking if company is bookmarked:', error);
      return false;
    }
  }

  /**
   * Add a company to bookmarks
   */
  async addBookmark(companyId: number, companyName: string, hasShortcut: boolean = false, shortcutId?: string): Promise<void> {
    try {
      const bookmarked = await this.getBookmarkedCompanies();
      
      // Check if already bookmarked
      const existingIndex = bookmarked.findIndex(company => company.companyId === companyId);
      
      if (existingIndex >= 0) {
        // Update existing bookmark
        bookmarked[existingIndex] = {
          ...bookmarked[existingIndex],
          companyName,
          hasShortcut,
          shortcutId,
          bookmarkedAt: new Date().toISOString(),
        };
      } else {
        // Add new bookmark
        bookmarked.push({
          companyId,
          companyName,
          bookmarkedAt: new Date().toISOString(),
          hasShortcut,
          shortcutId,
        });
      }

      await AsyncStorage.setItem(STORAGE_KEYS.BOOKMARKED_COMPANIES, JSON.stringify(bookmarked));
      console.log(`[BookmarkStorageService] Added bookmark for company: ${companyName} (ID: ${companyId})`);
    } catch (error) {
      console.error('[BookmarkStorageService] Error adding bookmark:', error);
      throw error;
    }
  }

  /**
   * Remove a company from bookmarks
   */
  async removeBookmark(companyId: number): Promise<void> {
    try {
      const bookmarked = await this.getBookmarkedCompanies();
      const filtered = bookmarked.filter(company => company.companyId !== companyId);
      
      await AsyncStorage.setItem(STORAGE_KEYS.BOOKMARKED_COMPANIES, JSON.stringify(filtered));
      console.log(`[BookmarkStorageService] Removed bookmark for company ID: ${companyId}`);
    } catch (error) {
      console.error('[BookmarkStorageService] Error removing bookmark:', error);
      throw error;
    }
  }

  /**
   * Update shortcut status for a bookmarked company
   */
  async updateShortcutStatus(companyId: number, hasShortcut: boolean, shortcutId?: string): Promise<void> {
    try {
      const bookmarked = await this.getBookmarkedCompanies();
      const companyIndex = bookmarked.findIndex(company => company.companyId === companyId);
      
      if (companyIndex >= 0) {
        bookmarked[companyIndex].hasShortcut = hasShortcut;
        bookmarked[companyIndex].shortcutId = shortcutId;
        
        await AsyncStorage.setItem(STORAGE_KEYS.BOOKMARKED_COMPANIES, JSON.stringify(bookmarked));
        console.log(`[BookmarkStorageService] Updated shortcut status for company ID: ${companyId}, hasShortcut: ${hasShortcut}`);
      }
    } catch (error) {
      console.error('[BookmarkStorageService] Error updating shortcut status:', error);
      throw error;
    }
  }

  /**
   * Get all company shortcuts
   */
  async getCompanyShortcuts(): Promise<CompanyShortcut[]> {
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEYS.COMPANY_SHORTCUTS);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('[BookmarkStorageService] Error getting company shortcuts:', error);
      return [];
    }
  }

  /**
   * Add a company shortcut record
   */
  async addShortcut(companyId: number, shortcutId: string): Promise<void> {
    try {
      const shortcuts = await this.getCompanyShortcuts();
      
      // Remove existing shortcut for this company if any
      const filtered = shortcuts.filter(shortcut => shortcut.companyId !== companyId);
      
      // Add new shortcut
      filtered.push({
        companyId,
        shortcutId,
        createdAt: new Date().toISOString(),
      });

      await AsyncStorage.setItem(STORAGE_KEYS.COMPANY_SHORTCUTS, JSON.stringify(filtered));
      console.log(`[BookmarkStorageService] Added shortcut record for company ID: ${companyId}, shortcutId: ${shortcutId}`);
    } catch (error) {
      console.error('[BookmarkStorageService] Error adding shortcut:', error);
      throw error;
    }
  }

  /**
   * Remove a company shortcut record
   */
  async removeShortcut(companyId: number): Promise<void> {
    try {
      const shortcuts = await this.getCompanyShortcuts();
      const filtered = shortcuts.filter(shortcut => shortcut.companyId !== companyId);
      
      await AsyncStorage.setItem(STORAGE_KEYS.COMPANY_SHORTCUTS, JSON.stringify(filtered));
      console.log(`[BookmarkStorageService] Removed shortcut record for company ID: ${companyId}`);
    } catch (error) {
      console.error('[BookmarkStorageService] Error removing shortcut:', error);
      throw error;
    }
  }

  /**
   * Check if a company has a shortcut
   */
  async hasShortcut(companyId: number): Promise<boolean> {
    try {
      const shortcuts = await this.getCompanyShortcuts();
      return shortcuts.some(shortcut => shortcut.companyId === companyId);
    } catch (error) {
      console.error('[BookmarkStorageService] Error checking shortcut:', error);
      return false;
    }
  }

  /**
   * Get shortcut ID for a company
   */
  async getShortcutId(companyId: number): Promise<string | null> {
    try {
      const shortcuts = await this.getCompanyShortcuts();
      const shortcut = shortcuts.find(shortcut => shortcut.companyId === companyId);
      return shortcut?.shortcutId || null;
    } catch (error) {
      console.error('[BookmarkStorageService] Error getting shortcut ID:', error);
      return null;
    }
  }

  /**
   * Clear all bookmarks and shortcuts (for testing/debugging)
   */
  async clearAll(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        STORAGE_KEYS.BOOKMARKED_COMPANIES,
        STORAGE_KEYS.COMPANY_SHORTCUTS,
      ]);
      console.log('[BookmarkStorageService] Cleared all bookmarks and shortcuts');
    } catch (error) {
      console.error('[BookmarkStorageService] Error clearing all data:', error);
      throw error;
    }
  }
}

export default new BookmarkStorageService();
