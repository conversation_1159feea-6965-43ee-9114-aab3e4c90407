import axios from 'axios';
import { CONFIG } from '../common/constant';

// Create an Axios instance with default settings for company categories
const companyCategoryApi = axios.create({
  baseURL: CONFIG.API_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  timeout: 30000, // 30 seconds timeout
});

// Add request interceptor for logging
companyCategoryApi.interceptors.request.use(
  (config) => {
    console.log(`[CompanyCategoryAPI] Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('[CompanyCategoryAPI] Request error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for logging
companyCategoryApi.interceptors.response.use(
  (response) => {
    console.log(`[CompanyCategoryAPI] Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('[CompanyCategoryAPI] Response error:', error.response?.status, error.response?.data);
    return Promise.reject(error);
  }
);

// API Response interfaces
export interface ApiCompanyCategory {
  id: number;
  companyId: number;
  categoryId: number;
}

export interface CompanyCategoryListResponse {
  message: string;
  data: ApiCompanyCategory[];
  status?: number;
  success?: boolean;
}

class CompanyCategoryApiService {
  /**
   * Fetch all company-category relationships
   */
  async fetchCompanyCategories(): Promise<ApiCompanyCategory[]> {
    try {
      console.log('[CompanyCategoryAPI] Fetching company-category relationships...');
      
      const response = await companyCategoryApi.get<CompanyCategoryListResponse>('/data/company-categories');
      
      const companyCategories = response.data.data || [];
      console.log(`[CompanyCategoryAPI] Fetched ${companyCategories.length} company-category relationships`);
      
      // Log first relationship for debugging
      if (companyCategories.length > 0) {
        console.log('[CompanyCategoryAPI] Sample company-category relationship:', JSON.stringify(companyCategories[0], null, 2));
      }
      
      return companyCategories;
    } catch (error) {
      console.error('[CompanyCategoryAPI] Error fetching company categories:', error);
      throw error;
    }
  }

  /**
   * Fetch company-category relationships by company ID
   */
  async fetchCompanyCategoriesByCompanyId(companyId: number): Promise<ApiCompanyCategory[]> {
    try {
      console.log(`[CompanyCategoryAPI] Fetching company-category relationships for company ${companyId}...`);
      
      const allCompanyCategories = await this.fetchCompanyCategories();
      const filteredCompanyCategories = allCompanyCategories.filter(cc => cc.companyId === companyId);
      
      console.log(`[CompanyCategoryAPI] Found ${filteredCompanyCategories.length} relationships for company ${companyId}`);
      
      return filteredCompanyCategories;
    } catch (error) {
      console.error(`[CompanyCategoryAPI] Error fetching company categories for company ${companyId}:`, error);
      throw error;
    }
  }

  /**
   * Fetch company-category relationships by category ID
   */
  async fetchCompanyCategoriesByCategoryId(categoryId: number): Promise<ApiCompanyCategory[]> {
    try {
      console.log(`[CompanyCategoryAPI] Fetching company-category relationships for category ${categoryId}...`);
      
      const allCompanyCategories = await this.fetchCompanyCategories();
      const filteredCompanyCategories = allCompanyCategories.filter(cc => cc.categoryId === categoryId);
      
      console.log(`[CompanyCategoryAPI] Found ${filteredCompanyCategories.length} relationships for category ${categoryId}`);
      
      return filteredCompanyCategories;
    } catch (error) {
      console.error(`[CompanyCategoryAPI] Error fetching company categories for category ${categoryId}:`, error);
      throw error;
    }
  }
}

export default new CompanyCategoryApiService();
