import { Platform, PermissionsAndroid } from 'react-native';
import { IOS } from '../../common';

// A mock voice service that simulates voice recognition
// This is used when the native module is not available

// Callback functions for voice events
let onSpeechStartCallback: (() => void) | null = null;
let onSpeechEndCallback: (() => void) | null = null;
let onSpeechResultsCallback: ((results: string[]) => void) | null = null;
let onSpeechErrorCallback: ((error: any) => void) | null = null;

// State variables
let isInitialized = false;
let isListening = false;
let mockTimer: NodeJS.Timeout | null = null;

// Mock search terms that will be returned when voice recognition is triggered
const mockSearchTerms = [
  'bank',
  'credit card',
  'loan',
  'insurance',
  'customer care',
  'help',
  'support',
  'contact',
  'account',
  'payment'
];

// Always return true to show the voice icon
export const isVoiceAvailable = (): boolean => {
  console.log('Using mock voice service - simulating voice recognition');
  return true;
};

// Request microphone permission
export const requestMicrophonePermission = async (): Promise<boolean> => {
  if (IOS) {
    return true; // iOS handles permissions differently
  }

  try {
    const granted = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
      {
        title: 'Microphone Permission',
        message: 'This app needs access to your microphone for voice search functionality.',
        buttonNeutral: 'Ask Me Later',
        buttonNegative: 'Cancel',
        buttonPositive: 'OK',
      },
    );

    return granted === PermissionsAndroid.RESULTS.GRANTED;
  } catch (err) {
    console.error('Failed to request microphone permission:', err);
    return true; // Return true anyway to allow the mock to work
  }
};

// Initialize voice recognition with callback functions
export const initializeVoiceRecognition = (
  onSpeechStart: () => void,
  onSpeechEnd: () => void,
  onSpeechResults: (results: string[]) => void,
  onSpeechError: (error: any) => void
): boolean => {
  console.log('Initializing mock voice recognition');

  // Store the callbacks for later use
  onSpeechStartCallback = onSpeechStart;
  onSpeechEndCallback = onSpeechEnd;
  onSpeechResultsCallback = onSpeechResults;
  onSpeechErrorCallback = onSpeechError;

  isInitialized = true;
  return true;
};

// Start listening for speech
export const startListening = async (): Promise<boolean> => {
  if (!isInitialized) {
    console.warn('Voice recognition not initialized');
    return false;
  }

  if (isListening) {
    console.warn('Already listening');
    return true;
  }

  console.log('Starting mock voice recognition');
  isListening = true;

  // Call the speech start callback
  if (onSpeechStartCallback) {
    onSpeechStartCallback();
  }

  // Simulate voice recognition with a timer
  mockTimer = setTimeout(() => {
    // Get a random search term
    const randomIndex = Math.floor(Math.random() * mockSearchTerms.length);
    const mockResult = mockSearchTerms[randomIndex];

    // Call the speech end callback
    if (onSpeechEndCallback) {
      onSpeechEndCallback();
    }

    // Call the speech results callback with the mock result
    if (onSpeechResultsCallback) {
      onSpeechResultsCallback([mockResult]);
    }

    isListening = false;
  }, 2000); // Simulate 2 seconds of listening

  return true;
};

// Stop listening for speech
export const stopListening = async (): Promise<boolean> => {
  if (!isListening) {
    console.warn('Not listening');
    return true;
  }

  console.log('Stopping mock voice recognition');

  // Clear the timer if it exists
  if (mockTimer) {
    clearTimeout(mockTimer);
    mockTimer = null;
  }

  // Call the speech end callback
  if (onSpeechEndCallback) {
    onSpeechEndCallback();
  }

  isListening = false;
  return true;
};

// Clean up voice recognition resources
export const destroyVoiceRecognition = async (): Promise<void> => {
  console.log('Destroying mock voice recognition');

  // Clear the timer if it exists
  if (mockTimer) {
    clearTimeout(mockTimer);
    mockTimer = null;
  }

  // Reset all callbacks
  onSpeechStartCallback = null;
  onSpeechEndCallback = null;
  onSpeechResultsCallback = null;
  onSpeechErrorCallback = null;

  isInitialized = false;
  isListening = false;
};
