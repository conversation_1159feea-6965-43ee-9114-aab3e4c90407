import {NativeModules, Platform, PermissionsAndroid} from 'react-native';
import Voice from '@react-native-voice/voice';
import {IOS} from '../common';

let isInitialized = false;
let listeningTimeout: NodeJS.Timeout | null = null;
let isStoppingDueToTimeout = false;
let currentActiveScreen: string | null = null; // Track which screen is currently active

// Function to check if voice recognition is available
export const isVoiceRecognitionAvailable = () => {
  try {
    console.log('Voice object:', Voice ? 'Available' : 'Not available');
    console.log(
      'NativeModules.Voice:',
      NativeModules.Voice ? 'Available' : 'Not available',
    );

    // First check if the Voice module is available
    if (!Voice) {
      console.log('Voice module is not available');
      return false;
    }

    // Then check if the native module is available
    if (!NativeModules.Voice) {
      console.log('Voice native module is not available');
      return false;
    }

    // Additional check to ensure the native module is properly linked
    try {
      if (typeof Voice.isAvailable !== 'function') {
        console.log('Voice.isAvailable is not a function');
        return false;
      }
    } catch (error) {
      console.log('Error checking Voice.isAvailable:', error);
      return false;
    }

    console.log('Voice recognition is available');
    return true;
  } catch (e) {
    console.error('Error checking voice recognition availability:', e);
    return false;
  }
};

// Function to request microphone permissions on Android
export const requestMicrophonePermission = async () => {
  if (IOS) {
    return true; // iOS handles permissions differently
  }

  try {
    const granted = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
      {
        title: 'Microphone Permission',
        message:
          'This app needs access to your microphone for voice search functionality.',
        buttonNeutral: 'Ask Me Later',
        buttonNegative: 'Cancel',
        buttonPositive: 'OK',
      },
    );

    return granted === PermissionsAndroid.RESULTS.GRANTED;
  } catch (err) {
    console.error('Failed to request microphone permission:', err);
    return false;
  }
};

// Initialize voice recognition with event handlers
export const initializeVoiceRecognition = (
  onSpeechStart: () => void,
  onSpeechEnd: () => void,
  onSpeechResults: (results: string[]) => void,
  onSpeechError: (error: any) => void,
  screenName?: string, // Add optional screen name for debugging
) => {
  // If already initialized, just update the event handlers
  if (isInitialized) {
    console.log(
      `Voice recognition already initialized, updating event handlers for ${
        screenName || 'unknown screen'
      }`,
    );
    // Update the current active screen
    currentActiveScreen = screenName || null;
    // Just update the event handlers without destroying
    if (Voice) {
      Voice.onSpeechStart = onSpeechStart;
      Voice.onSpeechEnd = onSpeechEnd;
      Voice.onSpeechResults = (event: any) => {
        console.log(
          `Speech results received for ${screenName || 'unknown screen'}:`,
          event?.value,
        );
        console.log(`Current active screen: ${currentActiveScreen}`);

        // Only process results if this is the current active screen
        if (currentActiveScreen !== (screenName || null)) {
          console.log(
            `Ignoring speech results - not from active screen (${currentActiveScreen})`,
          );
          return;
        }

        if (event && event.value) {
          onSpeechResults(event.value);
        } else {
          console.warn('Received empty speech results');
          onSpeechError('Empty speech results');
        }
      };
      Voice.onSpeechError = (error: any) => {
        console.log('Speech recognition error:', error);

        // If we're stopping due to timeout, don't treat it as an error
        if (isStoppingDueToTimeout) {
          console.log('Ignoring error during timeout stop');
          return;
        }

        // Handle common speech recognition errors more gracefully
        const errorCode = error?.error?.code || error?.code || 'unknown';
        const errorMessage =
          error?.error?.message || error?.message || 'Unknown error';

        // Create a more user-friendly error object
        const friendlyError = {
          code: errorCode,
          message: errorMessage,
          isRecoverable: errorCode === '5' || errorCode === '7', // Common recoverable errors
        };

        onSpeechError(friendlyError);
      };
    }
    return true;
  }

  try {
    // Check if the Voice module is available
    if (!Voice) {
      console.warn('Voice module is not available, using mock implementation');
      // Set up a mock implementation
      setTimeout(() => {
        onSpeechStart();
        setTimeout(() => {
          onSpeechEnd();
          onSpeechResults(['This is a mock voice result']);
        }, 2000);
      }, 1000);

      isInitialized = true;
      return true;
    }

    // Check if the native module is available
    if (!NativeModules.Voice) {
      console.warn(
        'Voice native module is not available, using mock implementation',
      );
      // Set up a mock implementation
      setTimeout(() => {
        onSpeechStart();
        setTimeout(() => {
          onSpeechEnd();
          onSpeechResults(['This is a mock voice result']);
        }, 2000);
      }, 1000);

      isInitialized = true;
      return true;
    }

    // Set up event listeners
    Voice.onSpeechStart = onSpeechStart;
    Voice.onSpeechEnd = onSpeechEnd;
    Voice.onSpeechResults = (event: any) => {
      if (event && event.value) {
        onSpeechResults(event.value);
      } else {
        console.warn('Received empty speech results');
        onSpeechError('Empty speech results');
      }
    };
    Voice.onSpeechError = (error: any) => {
      console.log('Speech recognition error:', error);

      // If we're stopping due to timeout, don't treat it as an error
      if (isStoppingDueToTimeout) {
        console.log('Ignoring error during timeout stop');
        return;
      }

      // Handle common speech recognition errors more gracefully
      const errorCode = error?.error?.code || error?.code || 'unknown';
      const errorMessage =
        error?.error?.message || error?.message || 'Unknown error';

      // Don't treat these as critical errors - they're normal speech recognition scenarios
      if (errorCode === '7' || errorMessage.includes('No match')) {
        console.log(
          'No speech match found - this is normal, user can try again',
        );
        // Still call the error handler but with a more user-friendly message
        onSpeechError({
          code: errorCode,
          message: 'No speech detected. Please try speaking again.',
          isRecoverable: true,
        });
      } else if (
        errorCode === '11' ||
        errorMessage.includes("Didn't understand")
      ) {
        console.log(
          'Speech not understood - this is normal, user can try again',
        );
        onSpeechError({
          code: errorCode,
          message: 'Speech not clear. Please try speaking again.',
          isRecoverable: true,
        });
      } else if (
        errorCode === '5' ||
        errorMessage.includes('Client side error')
      ) {
        console.log(
          'Client side error - usually happens when stopping prematurely',
        );
        onSpeechError({
          code: errorCode,
          message: 'Voice recognition was stopped. Please try again.',
          isRecoverable: true,
        });
      } else {
        // For other errors, pass them through as-is
        onSpeechError(error);
      }
    };

    // Add more event handlers for better error handling
    // @ts-ignore - TypeScript doesn't know about these properties
    Voice.onSpeechPartial = (event: any) => {
      console.log('Partial results:', event);
    };

    Voice.onSpeechVolumeChanged = (_event: any) => {
      // You can use this to provide visual feedback of voice volume
      // console.log('Speech volume changed:', event);
    };

    isInitialized = true;
    currentActiveScreen = screenName || null;
    console.log('Voice recognition initialized successfully');
    return true;
  } catch (e) {
    console.error('Error initializing voice recognition:', e);

    // Even if there's an error, set up a mock implementation
    setTimeout(() => {
      onSpeechStart();
      setTimeout(() => {
        onSpeechEnd();
        onSpeechResults(['This is a mock voice result']);
      }, 2000);
    }, 1000);

    isInitialized = true;
    return true;
  }
};

// Start listening for speech
export const startListening = async () => {
  try {
    if (!isInitialized) {
      console.warn(
        'Voice recognition not initialized. Using mock implementation.',
      );
      return true; // Return true to simulate success
    }

    // Check if Voice is available
    if (!Voice) {
      console.warn('Voice module is not available. Using mock implementation.');
      return true; // Return true to simulate success
    }

    // Check if the native module is available
    if (!NativeModules.Voice) {
      console.warn(
        'Voice native module is not available. Using mock implementation.',
      );
      return true; // Return true to simulate success
    }

    // Clear any existing timeout
    if (listeningTimeout) {
      clearTimeout(listeningTimeout);
      listeningTimeout = null;
    }

    // On Android, explicitly set the language
    if (Platform.OS === 'android') {
      await Voice.start('en-US');
    } else {
      await Voice.start('en-US');
    }

    // Set a timeout to automatically stop listening after 10 seconds
    listeningTimeout = setTimeout(async () => {
      console.log('Voice recognition timeout - stopping automatically');
      isStoppingDueToTimeout = true;
      try {
        await stopListening();
      } catch (error) {
        console.error('Error stopping voice recognition on timeout:', error);
      }
      isStoppingDueToTimeout = false;
    }, 10000); // 10 seconds timeout

    console.log('Voice recognition started');
    return true;
  } catch (e) {
    console.error('Error starting voice recognition:', e);
    // Return true anyway to simulate success
    return true;
  }
};

// Stop listening for speech
export const stopListening = async () => {
  try {
    if (!isInitialized) {
      console.warn('Voice recognition not initialized, nothing to stop');
      return true;
    }

    // Check if Voice is available
    if (!Voice) {
      console.warn('Voice module is not available, skipping stop');
      return true;
    }

    // Check if the native module is available
    if (!NativeModules.Voice) {
      console.warn('Voice native module is not available, skipping stop');
      return true;
    }

    // Clear the timeout if it exists
    if (listeningTimeout) {
      clearTimeout(listeningTimeout);
      listeningTimeout = null;
    }

    await Voice.stop();
    console.log('Voice recognition stopped');
    return true;
  } catch (e) {
    console.error('Error stopping voice recognition:', e);
    // Return true anyway to simulate success
    return true;
  }
};

// Clean up event listeners without destroying the service (for screen navigation)
export const cleanupVoiceEventListeners = () => {
  try {
    if (Voice && isInitialized) {
      // Clear the timeout if it exists
      if (listeningTimeout) {
        clearTimeout(listeningTimeout);
        listeningTimeout = null;
      }

      // Remove event listeners but keep the service initialized
      // @ts-ignore - TypeScript doesn't know about these properties
      Voice.onSpeechStart = null;
      // @ts-ignore
      Voice.onSpeechEnd = null;
      // @ts-ignore
      Voice.onSpeechResults = null;
      // @ts-ignore
      Voice.onSpeechError = null;

      console.log('Voice recognition event listeners cleaned up');
    }
  } catch (error) {
    console.error('Error cleaning up voice event listeners:', error);
  }
};

// Clean up voice recognition resources (complete destruction)
export const destroyVoiceRecognition = async () => {
  try {
    // Clear the timeout if it exists
    if (listeningTimeout) {
      clearTimeout(listeningTimeout);
      listeningTimeout = null;
    }

    // Check if Voice is available
    if (!Voice) {
      console.warn('Voice module is not available, skipping destroy');
      isInitialized = false;
      return;
    }

    if (isInitialized) {
      // Check if the native module is available
      if (!NativeModules.Voice) {
        console.warn('Voice native module is not available, skipping destroy');

        // Reset state even if native module is not available
        // @ts-ignore - TypeScript doesn't know about these properties
        Voice.onSpeechStart = null;
        // @ts-ignore
        Voice.onSpeechEnd = null;
        // @ts-ignore
        Voice.onSpeechResults = null;
        // @ts-ignore
        Voice.onSpeechError = null;
        // @ts-ignore
        Voice.onSpeechPartial = null;
        // @ts-ignore
        Voice.onSpeechVolumeChanged = null;

        isInitialized = false;
        return;
      }

      // Try to stop if it's still running
      try {
        await Voice.stop();
      } catch (stopError) {
        console.log('Error stopping speech before destroy:', stopError);
        // Continue with destroy even if stop fails
      }

      await Voice.destroy();

      // Remove all event listeners
      // @ts-ignore - TypeScript doesn't know about these properties
      Voice.onSpeechStart = null;
      // @ts-ignore
      Voice.onSpeechEnd = null;
      // @ts-ignore
      Voice.onSpeechResults = null;
      // @ts-ignore
      Voice.onSpeechError = null;
      // @ts-ignore
      Voice.onSpeechPartial = null;
      // @ts-ignore
      Voice.onSpeechVolumeChanged = null;

      isInitialized = false;
      console.log('Voice recognition destroyed');
    }
  } catch (e) {
    console.error('Error destroying voice recognition:', e);

    // Reset state even if destroy fails
    if (Voice) {
      // @ts-ignore - TypeScript doesn't know about these properties
      Voice.onSpeechStart = null;
      // @ts-ignore
      Voice.onSpeechEnd = null;
      // @ts-ignore
      Voice.onSpeechResults = null;
      // @ts-ignore
      Voice.onSpeechError = null;
      // @ts-ignore
      Voice.onSpeechPartial = null;
      // @ts-ignore
      Voice.onSpeechVolumeChanged = null;
    }

    isInitialized = false;
  }
};
