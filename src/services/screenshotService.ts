import { captureScreen } from 'react-native-view-shot';
import { Alert } from 'react-native';
import Share from 'react-native-share';

class ScreenshotService {
  /**
   * Captures the full screen and returns the URI of the screenshot.
   * @returns {Promise<string | null>} The URI of the screenshot or null if failed.
   */
  static async captureFullScreen(): Promise<string | null> {
    try {
      const screenshotUri = await captureScreen({
        format: 'jpg',
        quality: 0.8,
      });

      if (!screenshotUri) {
        Alert.alert('Error', 'Failed to capture screenshot.');
        return null;
      }

      return screenshotUri;
    } catch (error) {
      console.error('Error capturing screenshot:', error);
      Alert.alert('Error', 'An error occurred while capturing the screenshot.');
      return null;
    }
  }

  /**
   * Shares the screenshot using React Native's Share API.
   * @param screenshotUri The URI of the screenshot to share.
   */
  static async shareScreenshot(screenshotUri: string): Promise<void> {
    try {
      const fileUri = screenshotUri.startsWith('file://') ? screenshotUri : `file://${screenshotUri}`;
      await Share.open({
        title: 'Share Screenshot',
        message: 'Check out this numbers!',
        url: fileUri,
      });
    } catch (error) {
      //console.error('Error sharing screenshot:', error);
    }
  }
}

export default ScreenshotService;