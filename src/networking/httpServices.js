export const apiRequest = async (url, options) => {
  try {
    const response = await fetch(url, options);

    const responseData = await response.json();

    console.info(`
      ====================================
      ============== Request =============
      URL :- ${url}
      Method :- ${options.method}
      Header :- ${JSON.stringify(options.headers, null, 2)}
      Parameter :- ${JSON.stringify(
        options.body ? JSON.parse(options.body) : {},
        null,
        2,
      )}
      ============= Response =============
      StatusCode :- ${response.status}
      Response :- ${JSON.stringify(responseData, null, 2)}
      ====================================
          `);

    if (!response.ok) {
      throw new Error(responseData.message || 'API request failed');
    }

    return responseData;
  } catch (error) {
    throw error;
  }
};
