import React, {useState} from 'react';
import {StyleSheet, FlatList, SafeAreaView, Alert} from 'react-native';
import {Text, Button} from 'react-native-paper';
import CustomCheckboxCard from '../../components/CustomCheckboxCard';
import {useNavigation} from '@react-navigation/native';
import CategoriesModalScreen from '../categoriesModal/categoriesModalScreen';
import commonStyles from '../../common/commonStyles';

const selctCategoriesList = [
  'Post Office',
  'RTO',
  'Mutual Fund',
  'Insurance',
  'Passport',
  'AIIMS',
];

const SelectCategories = () => {
  const [isModalVisible, setModalVisible] = useState(false);
  const [checkedItems, setCheckedItems] = useState<{[key: string]: boolean}>({
    'Post Office': true,
    RTO: true,
    'Mutual Fund': false,
    Insurance: true,
    Passport: false,
    AIIMS: true,
  });

  const toggleCheckbox = (item: string) => {
    setCheckedItems(prev => ({
      ...prev,
      [item]: !prev[item],
    }));
  };

  const toggleModal = () => {
    setModalVisible(!isModalVisible);
  };

  return (
    <SafeAreaView style={styles.container}>
      <Text style={styles.title}>Select Categories to show open /close</Text>

      <FlatList
        data={selctCategoriesList}
        keyExtractor={item => item}
        renderItem={({item}) => (
          <CustomCheckboxCard
            label={item}
            checked={checkedItems[item]}
            onToggle={() => toggleCheckbox(item)}
          />
        )}
      />
      <Button
        mode="contained"
        onPress={toggleModal}
        style={commonStyles.bottomButton}
        labelStyle={commonStyles.bottomButtonLabel}
        buttonColor="#0a1d50">
        Save
      </Button>
      <CategoriesModalScreen
        visible={isModalVisible}
        title="Categories Modal"
        content="This is a sample modal message."
        onClose={() => setModalVisible(false)}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 15,
    backgroundColor: '#fff',
  },
  menuButton: {
    marginLeft: 15,
  },
  title: {
    fontSize: 28,
    marginBottom: 20,
    fontFamily: 'Poppins-Medium',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    padding: 20,
    backgroundColor: '#fff',
    borderRadius: 10,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  modalText: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
  },
  closeButton: {
    backgroundColor: '#0a1d50',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
  },
  closeButtonText: {
    color: '#fff',
    fontSize: 16,
  },
});

export default SelectCategories;
