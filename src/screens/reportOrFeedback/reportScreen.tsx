import React, {useState} from 'react';
import {
  StyleSheet,
  SafeAreaView,
  TextInput,
  View,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import {Text, Button} from 'react-native-paper';
import {FONTS} from '../../common/constant';
import {COLORS} from '../../common/constant';
import commonStyles from '../../common/commonStyles';
import {showErrorToast, showSuccessToast} from '../../utils/showToast';
import axios from 'axios';
import {CONFIG, VALIDATION_TYPE} from '../../common/constant';

const ReportScreen = () => {
  const [name, setName] = useState('');
  const [text, setText] = useState('');
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSaveReportSelection = () => {
    // Handle save action here
    pushUserReport();
  };

  // Fetch vote counts for company numbers
  const pushUserReport = async () => {
    if (name === '') {
      showErrorToast('Please enter name');
      return;
    }

    if (email === '') {
      showErrorToast('Please enter email');
      return;
    }

    if (!email.match(VALIDATION_TYPE.EMAIL)) {
      showErrorToast('Please enter a valid email');
      return;
    }

    if (text === '') {
      showErrorToast('Please enter text');
      return;
    }
    setLoading(true); // Start loading
    try {
      const response = await axios.post(
        `${CONFIG.API_URL}/feedback`,
        {
          userName: name,
          userEmail: email,
          comment: text,
        },
        {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
          },
          timeout: 30000, // 30 seconds timeout
        },
      );

      console.info(`
        ====================================
        ============== Request =============
        URL: ${CONFIG.API_URL}/feedback
        Method: POST
        Headers: ${JSON.stringify(
          {
            Accept: 'application/json',
            'Content-Type': 'application/json',
          },
          null,
          2,
        )}
        Body: ${JSON.stringify(
          {userName: name, userEmail: email, comment: text},
          null,
          2,
        )}
        ============= Response =============
        StatusCode: ${response.status}
        Response: ${JSON.stringify(response.data, null, 2)}
        ====================================
      `);

      if (response.status === 201 && response.data?.success) {
        showSuccessToast('Report submitted successfully');
        setName('');
        setEmail('');
        setText('');
      }
    } catch (err: any) {
      console.error('Feedback API Error:', err);
      // Don't show error toast for vote API failures as it's not critical
      // Just log the error for debugging
      if (err.response) {
        console.error(
          `Feedback API Server Error: ${err.response.status}`,
          err.response.data,
        );
      } else if (err.request) {
        console.error('Feedback API Network Error:', err.request);
      } else {
        console.error('Feedback API Unknown Error:', err.message);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={[commonStyles.textHereInputView, styles.textInputView]}>
            <Text style={commonStyles.textHereInputHeader}>Name</Text>
            <TextInput
              style={styles.textInput}
              placeholder=""
              placeholderTextColor={'#B7B7B7'}
              value={name}
              onChangeText={setName}
              autoCorrect={false}
              spellCheck={false}
            />
          </View>
          <View style={[commonStyles.textHereInputView, styles.textInputView]}>
            <Text style={commonStyles.textHereInputHeader}>Email</Text>
            <TextInput
              style={styles.textInput}
              placeholder=""
              placeholderTextColor={'#B7B7B7'}
              value={email}
              onChangeText={setEmail}
              textAlignVertical="top"
              autoCorrect={false}
              spellCheck={false}
              keyboardType="email-address"
            />
          </View>
          <View style={[commonStyles.textHereInputView, styles.textInputView]}>
            <Text style={commonStyles.textHereInputHeader}>Comment</Text>
            <TextInput
              style={commonStyles.textHereView}
              multiline
              placeholder="text here..."
              value={text}
              onChangeText={setText}
              textAlignVertical="top"
              autoCorrect={false}
              spellCheck={false}
            />
          </View>
        </ScrollView>
        {loading ? (
          <ActivityIndicator
            size="large"
            color="#0a1d50"
            style={styles.loadingIndicator}
          />
        ) : (
          <Button
            mode="contained"
            onPress={handleSaveReportSelection}
            style={styles.saveButton}
            labelStyle={styles.saveButtonLabel}
            buttonColor={'#0a1d50'}>
            Submit
          </Button>
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.WHITE,
  },
  content: {
    flex: 1,
    justifyContent: 'space-between',
  },
  scrollContent: {
    paddingBottom: 20,
  },
  textInputView: {marginTop: 15, marginLeft: 15, marginRight: 15},
  textInput: {
    marginTop: 5,
    fontFamily: FONTS.POPPINS.REGULAR,
    fontSize: 17,
    height: 48,
    padding: 10,
    color: COLORS.BLACK,
    borderColor: COLORS.BORDER_COLOR,
    borderWidth: 1,
    borderRadius: 10,
  },
  saveButton: {
    marginLeft: 15,
    marginRight: 15,
    marginTop: 20,
    marginBottom: 60,
    paddingVertical: 6,
    borderRadius: 10,
  },
  saveButtonLabel: {
    fontSize: 16,
    color: '#fff',
    fontFamily: FONTS.POPPINS.MEDIUM,
  },
  loadingIndicator: {
    marginBottom: 65,
  },
});

export default ReportScreen;
