import React, {useState, useEffect, useCallback} from 'react';
import {useFocusEffect} from '@react-navigation/native';
import {
  StyleSheet,
  FlatList,
  SafeAreaView,
  View,
  Text,
  ActivityIndicator,
  Modal,
  Image,
  TouchableOpacity,
} from 'react-native';
import mobileAds, {NativeAd} from 'react-native-google-mobile-ads';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import CustomSearchBar from '../../components/CustomSearchBar';
import CustomCategoriesCard from '../../components/CustomCategoriesCard';
import ShortcutButton from '../../components/ShortcutButton';
import ShortcutDebugPanel from '../../components/ShortcutDebugPanel'; // TEMPORARY FOR TESTING
import SyncStatusIndicator from '../../components/SyncStatusIndicator';
import DatabaseTestPanel from '../../components/DatabaseTestPanel'; // TEMPORARY FOR TESTING
import commonStyles from '../../common/commonStyles';
import {debounce} from 'lodash';
import {showErrorToast} from '../../utils/showToast';
import watermelonCategoryRepository, {
  CategoryData,
} from '../../database/watermelon/repositories/categoryRepository';
import {useBackgroundSync} from '../../hooks/useBackgroundSync';
import {useAppNavigation} from '../../hooks/useAppNavigation';
import {useAppShortcuts} from '../../hooks/useAppShortcuts';
import {useNetworkState} from '../../utils/networkStateManager';
import axios from 'axios';
import {CONFIG} from '../../common/constant';
import {fetchCategory} from '../../components/redux/api/category';
import BannerAdComponent from '../../components/ads/BannerAdComponent';
import AdTestButton from '../../components/ads/AdTestButton';
import {BannerAdSize} from 'react-native-google-mobile-ads';
import CustomNativeAdCard from '../../components/ads/CustomNativeAdCard';
import adMobService from '../../services/adMobService';

const CategoriesScreen = () => {
  // Get safe area insets for proper bottom padding
  const insets = useSafeAreaInsets();

  const {isConnected} = useNetworkState();

  // Use background sync hook to monitor sync status
  const categoriesSync = useBackgroundSync('categories');

  // Navigation and shortcuts
  const navigation = useAppNavigation();
  const {
    isShortcutSupported,
    createCategoriesShortcut,
    isLoading: shortcutLoading,
  } = useAppShortcuts(navigation);

  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [isScreenFocused, setIsScreenFocused] = useState(false);

  const [originalCategoryList, setOriginalCategoryList] = useState<
    CategoryData[]
  >([]); // Store the original list
  const [categoryList, setCategoryList] = useState<CategoryData[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  // TEMPORARY: Test panel state
  const [showTestPanel, setShowTestPanel] = useState(false);
  const [showShortcutDebug, setShowShortcutDebug] = useState(false);

  const [nativeAd, setNativeAd] = useState<NativeAd | null>(null);

  // Load data from local database on component mount
  useEffect(() => {
    adMobService.initiateNativeAd().then(ad => setNativeAd(ad));
    loadCategoriesFromLocalDB();
  }, []);

  // Reload data when background sync completes
  useEffect(() => {
    if (categoriesSync.status === 'completed') {
      loadCategoriesFromLocalDB();
    }
  }, [categoriesSync.status]);

  // Reload data when search query changes
  {
    /*
  useEffect(() => {
    if (originalCategoryList.length > 0) {
      loadCategoriesFromLocalDB();
    }
  }, [searchQuery]);
  */
  }

  // Handle screen focus for voice recognition
  useFocusEffect(
    useCallback(() => {
      console.log('[CategoriesScreen] Screen focused');
      console.log('[CategoriesScreen] Setting isScreenFocused to true');
      setIsScreenFocused(true);

      return () => {
        console.log('[CategoriesScreen] Screen unfocused');
        console.log('[CategoriesScreen] Setting isScreenFocused to false');
        setIsScreenFocused(false);
      };
    }, []),
  );

  // Load categories from local database only
  // Function to load categories only from local DB (no API calls)
  const loadCategoriesFromLocalDBOnly = async (searchTextOverride?: string) => {
    try {
      console.log('Loading categories from local DB only...');
      const localCategories = await watermelonCategoryRepository.getAll();
      console.log('Loaded', localCategories.length, 'categories from local DB');

      if (localCategories.length > 0) {
        // Sort categories by priority and name
        const sortedCategories = localCategories.sort(
          (a: CategoryData, b: CategoryData) => {
            const aPriority = a.categoryPriority || 999999;
            const bPriority = b.categoryPriority || 999999;
            if (aPriority !== bPriority) {
              return aPriority - bPriority;
            }
            return a.name.localeCompare(b.name);
          },
        );

        console.log(
          'Setting originalCategoryList with',
          sortedCategories.length,
          'categories',
        );
        setOriginalCategoryList(sortedCategories);

        // Apply search filter if provided
        if (searchTextOverride && searchTextOverride.trim().length >= 3) {
          console.log('Applying search filter:', searchTextOverride);
          const trimmedQuery = searchTextOverride.trim().toLowerCase();
          const filteredCategories = sortedCategories.filter(
            (category: CategoryData) =>
              category.name.toLowerCase().trim().includes(trimmedQuery),
          );
          console.log('Filtered to', filteredCategories.length, 'categories');
          setCategoryList(filteredCategories);
        } else {
          setCategoryList(sortedCategories);
        }
      } else {
        console.log('No categories found in local DB');
        setCategoryList([]);
        setOriginalCategoryList([]);
      }
    } catch (error) {
      console.error('Error loading categories from local DB only:', error);
      setCategoryList([]);
      setOriginalCategoryList([]);
    }
  };

  const loadCategoriesFromLocalDB = async (searchTextOverride?: string) => {
    setLoading(true);
    if (isConnected) {
      try {
        const response = await axios.get(
          'https://india-customer-care-api.apps.openxcell.dev/app/v1/category',
          {
            headers: {
              'Content-Type': 'application/json',
              // Add any other headers needed by your API (e.g., authorization tokens)
            },
          },
        );
        console.info(`
          ===================================
          ============== Request =============
          URL: ${CONFIG.API_URL}/category
          ============= Response =============
          StatusCode: ${response.status}
          ====================================
        `);

        if (response.status === 200) {
          const categoryData = response.data.data;

          const transformedData: CategoryData[] = categoryData.map(
            (item: any) => ({
              apiCategoryId: item.categoryId,
              categoryId: String(item.categoryId),
              name: item.name,
              iconUrl: item.iconUrl,
              isActive: item.isActive ? 1 : 0, // Convert boolean to number if needed
              categoryPriority: item.categoryPriority || 999, // Default to high value if not provided
              // categoryId will be generated/stored later (e.g., WatermelonDB ID)
              created_at: new Date(), // Optional: assign if needed
              updated_at: new Date(),
            }),
          );

          setOriginalCategoryList(transformedData);
          setCategoryList(transformedData);
          console.log('CategoryData from API:', transformedData.length);
        }
      } catch (err: any) {
        setCategoryList([]);
        setOriginalCategoryList([]);
        setLoading(false);
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    } else {
      try {
        const localCategories = await watermelonCategoryRepository.getAll();

        console.log('Loaded categories from local DB:', localCategories.length);

        // Deduplicate categories by ID
        const uniqueCategories = Array.from(
          new Map(
            localCategories.map(item => [item.categoryId, item]),
          ).values(),
        );

        // Sort categories by priority (lower values first), then alphabetically by name
        const sortedCategories = uniqueCategories.sort((a, b) => {
          // First sort by priority (lower values first)
          const priorityA = a.categoryPriority || 999;
          const priorityB = b.categoryPriority || 999;

          if (priorityA !== priorityB) {
            return priorityA - priorityB;
          }

          // If priorities are equal, sort alphabetically by name
          return a.name.localeCompare(b.name, undefined, {sensitivity: 'base'});
        });

        // Save the original sorted list for search filtering
        console.log(
          'Setting originalCategoryList with',
          sortedCategories.length,
          'categories',
        );
        // Apply search filter if query exists (use override if provided)
        const currentSearchQuery = searchTextOverride || searchQuery;

        console.log('Current searchQuery:', searchQuery);
        console.log('searchTextOverride:', searchTextOverride);
        console.log('currentSearchQuery:', currentSearchQuery);
        setOriginalCategoryList(sortedCategories);
        if (currentSearchQuery && currentSearchQuery.trim().length >= 3) {
          const trimmedQuery = currentSearchQuery.trim().toLowerCase();
          const filteredCategories = sortedCategories.filter(
            (category: CategoryData) =>
              category.name.toLowerCase().trim().includes(trimmedQuery),
          );
          // Sort filtered results by priority (lower values first), then alphabetically
          const sortedFilteredCategories = filteredCategories.sort((a, b) => {
            // First sort by priority (lower values first)
            const priorityA = a.categoryPriority || 999;
            const priorityB = b.categoryPriority || 999;

            if (priorityA !== priorityB) {
              return priorityA - priorityB;
            }

            // If priorities are equal, sort alphabetically by name
            return a.name.localeCompare(b.name, undefined, {
              sensitivity: 'base',
            });
          });
          setCategoryList(sortedFilteredCategories);
        } else {
          setCategoryList(sortedCategories);
        }
      } catch (err) {
        console.error('Error loading categories from local DB:', err);
        showErrorToast('Failed to load categories from local storage');
        setCategoryList([]);
        setOriginalCategoryList([]);
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    }
  };

  const filterSearchedData = (searchText: string) => {
    console.log('Filtering categories with search text:', searchText);
    console.log('Original category list:', originalCategoryList.length);

    // If originalCategoryList is empty, try to reload from local DB
    if (originalCategoryList.length === 0) {
      console.log(
        'Original category list is empty, reloading from local DB...',
      );
      // Load categories directly from local DB and apply search
      loadCategoriesFromLocalDBOnly(searchText);
      return;
    }

    if (!searchText || searchText.trim() === '') {
      // Restore the original list if search text is empty (already sorted)
      console.log('Restoring original category list');
      setCategoryList([...originalCategoryList]); // Ensure a new array reference
      return;
    }

    // Filter categories based on the search text (case-insensitive)
    const trimmedSearchText = searchText.trim().toLowerCase();
    const filteredCategories = originalCategoryList.filter(category =>
      category.name.toLowerCase().trim().includes(trimmedSearchText),
    );

    // Sort filtered results by priority (lower values first), then alphabetically
    const sortedFilteredCategories = filteredCategories.sort((a, b) => {
      // First sort by priority (lower values first)
      const priorityA = a.categoryPriority || 999;
      const priorityB = b.categoryPriority || 999;

      if (priorityA !== priorityB) {
        return priorityA - priorityB;
      }

      // If priorities are equal, sort alphabetically by name
      return a.name.localeCompare(b.name, undefined, {sensitivity: 'base'});
    });

    setCategoryList(sortedFilteredCategories);
  };

  const debouncedSearch = useCallback(
    debounce((text: string) => {
      console.log('Debounced search text:', text);
      filterSearchedData(text);
    }, 300),
    [], // Remove originalCategoryList dependency to prevent recreation
  );

  const handleSearchChange = (text: string) => {
    console.log('handleSearchChange called with:', text);
    console.log(
      'Current originalCategoryList length:',
      originalCategoryList.length,
    );
    setSearchQuery(text);
    debouncedSearch(text);
  };

  // Render empty list message
  const renderEmptyList = () => {
    if (loading && !refreshing) return null;

    return (
      <View style={commonStyles.emptyContainer}>
        <Text style={commonStyles.emptyText}>
          {searchQuery
            ? 'No categories match your search.'
            : 'No categories found.'}
        </Text>
      </View>
    );
  };

  // Render footer (loading indicator for pagination)
  const renderFooter = () => {
    if (!loading || refreshing) return null;
    return (
      <View style={commonStyles.footerLoader}>
        <ActivityIndicator size="large" color="#0000ff" />
        <Text style={commonStyles.footerText}>Loading more categories...</Text>
      </View>
    );
  };

  // Handle refresh - simply reload from local database
  const handleRefresh = () => {
    setRefreshing(true);
    loadCategoriesFromLocalDB();
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.mainContent}>
        <CustomSearchBar
          onSearch={handleSearchChange}
          onVoiceResult={result => {
            handleSearchChange(result);
          }}
          isSearching={false}
          initialValue={searchQuery}
          placeholder="Search categories"
          showVoiceSearch={true}
          screenName="CategoriesScreen"
          isScreenFocused={isScreenFocused}
        />

        {/* Add to Home Screen shortcut button 
        {isShortcutSupported && (
          <View style={{paddingHorizontal: 15, paddingTop: 10}}>
            <ShortcutButton
              title="Add Categories to Home Screen"
              onPress={createCategoriesShortcut}
              isLoading={shortcutLoading}
            />
          </View>
        )} */}

        {/* TEMPORARY: Shortcut Debug Button
        <TouchableOpacity
          style={[styles.testButton, {backgroundColor: '#28a745'}]}
          onPress={() => setShowShortcutDebug(true)}>
          <Text style={styles.testButtonText}>🔧 Shortcut Debug</Text>
        </TouchableOpacity>  */}

        {/* TEMPORARY: Test Panel Button - Hidden for now
        <TouchableOpacity
          style={styles.testButton}
          onPress={() => setShowTestPanel(true)}>
          <Text style={styles.testButtonText}>🧪 DB Test Panel</Text>
        </TouchableOpacity>
        */}
        {/* Sync status indicator temporarily hidden - background sync continues */}
        {/* <SyncStatusIndicator showOnlyWhenActive={true} /> */}

        {/* Ad Test Buttons 
        <AdTestButton /> */}

        <FlatList
          style={{padding: 15, flex: 1}}
          data={categoryList}
          keyExtractor={(item, index) => `${item.apiCategoryId}-${index}`}
          renderItem={({item, index}) => (
            <>
              {/* Show ad after the 3rd item 
              {index === 3 && nativeAd && <CustomNativeAdCard ad={nativeAd} />}*/}
              <CustomCategoriesCard item={item} />
            </>
          )}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          onEndReachedThreshold={0.3}
          ListEmptyComponent={renderEmptyList}
          ListFooterComponent={renderFooter}
          refreshing={refreshing}
          onRefresh={handleRefresh}
          contentContainerStyle={
            categoryList.length === 0
              ? commonStyles.fullHeight
              : {
                  paddingBottom: Math.max(insets.bottom, 30), // Ensure minimum 20px bottom padding
                }
          }
        />
      </View>

      {/* TEMPORARY: Shortcut Debug Modal 
      <Modal
        visible={showShortcutDebug}
        animationType="slide"
        presentationStyle="pageSheet">
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowShortcutDebug(false)}>
              <Text style={styles.closeButtonText}>✕ Close</Text>
            </TouchableOpacity>
          </View>
          <ShortcutDebugPanel />
        </SafeAreaView>
      </Modal> 
      */}

      {/* TEMPORARY: Test Panel Modal */}
      <Modal
        visible={showTestPanel}
        animationType="slide"
        presentationStyle="pageSheet">
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowTestPanel(false)}>
              <Text style={styles.closeButtonText}>✕ Close</Text>
            </TouchableOpacity>
          </View>
          <DatabaseTestPanel />
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  mainContent: {
    flex: 1,
  },
  // TEMPORARY: Test panel styles
  testButton: {
    backgroundColor: '#007bff',
    margin: 15,
    padding: 10,
    borderRadius: 8,
    alignItems: 'center',
  },
  testButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  closeButton: {
    backgroundColor: '#dc3545',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  closeButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
});

export default CategoriesScreen;
