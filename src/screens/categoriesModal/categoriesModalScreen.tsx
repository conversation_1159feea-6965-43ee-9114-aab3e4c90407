import React from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  FlatList,
} from 'react-native';
import {Images} from '../../assets';
import {COLORS, FONTS} from '../../common/constant';

interface CustomModalProps {
  visible: boolean;
  title: string;
  content: string;
  onClose: () => void;
}
const CategoriesModalScreen = ({
  visible,
  title,
  content,
  onClose,
}: CustomModalProps) => {
  const selctCategoriesList = [
    {
      id: 1,
      title: 'RTO',
      details:
        'RTO office is currently closed. Please visit during working hours.',
    },
    {
      id: 2,
      title: 'Insurance',
      details:
        'Insurance office is closed every 2nd & 4th Saturday. Please visit during working hours.',
    },
    {
      id: 3,
      title: 'Insurance',
      details:
        'Insurance office is closed every 2nd & 4th Saturday. Please visit during working hours.',
    },
    {
      id: 4,
      title: 'Insurance',
      details:
        'Insurance office is closed every 2nd & 4th Saturday. Please visit during working hours.',
    },
  ];

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <TouchableOpacity
            style={{height: 44, alignItems: 'flex-end'}}
            onPress={onClose}>
            <Image style={styles.closeIcon} source={Images.ic_close} />
          </TouchableOpacity>
          <FlatList
            data={selctCategoriesList}
            keyExtractor={item => item.id.toString()}
            renderItem={({item}) => (
              <View style={styles.itemContainer}>
                <Text style={styles.itemTitle}>{item.title}</Text>
                <Text style={styles.itemText}>{item.details}</Text>
              </View>
            )}
            style={{padding: 2}}
          />
        </View>
      </View>
    </Modal>
  );
};
//<Text style={styles.modalText}>{content}</Text>
const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    padding: 15,
    backgroundColor: 'white',
    borderRadius: 10,
    maxHeight: '95%',
  },
  itemTitle: {
    fontSize: 20,
    fontFamily: FONTS.POPPINS.MEDIUM,
    marginBottom: 5,
    marginTop: 5,
  },
  itemText: {
    fontSize: 15,
    fontFamily: FONTS.POPPINS.REGULAR,
    textAlign: 'center',
  },
  itemContainer: {
    padding: 10,
    borderWidth: 1,
    borderColor: COLORS.BORDER_COLOR,
    marginBottom: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  closeIcon: {
    marginLeft: 3,
    height: 30,
    width: 30,
    marginRight: 5,
  },
});

export default CategoriesModalScreen;
