import React from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  Alert,
} from 'react-native';
import { Button, Text } from 'react-native-paper';
import { useInterstitialAd } from '../../hooks/useInterstitialAd';
import { useRewardedAd } from '../../hooks/useRewardedAd';
import BannerAdComponent from '../../components/ads/BannerAdComponent';
import { BannerAdSize } from 'react-native-google-mobile-ads';
import commonStyles from '../../common/commonStyles';

const AdExampleScreen = () => {
  const { showInterstitialAd, isAdMobReady: interstitialReady } = useInterstitialAd();
  const { showRewardedAd, isAdMobReady: rewardedReady } = useRewardedAd();

  const handleShowInterstitial = async () => {
    if (!interstitialReady) {
      Alert.alert('AdMob Not Ready', 'AdMob is still initializing. Please try again in a moment.');
      return;
    }

    await showInterstitialAd();
  };

  const handleShowRewarded = async () => {
    if (!rewardedReady) {
      Alert.alert('AdMob Not Ready', 'AdMob is still initializing. Please try again in a moment.');
      return;
    }

    const rewarded = await showRewardedAd();
    
    if (rewarded) {
      Alert.alert('Reward Earned!', 'You have successfully watched the rewarded ad and earned your reward!');
    } else {
      Alert.alert('No Reward', 'You did not complete watching the rewarded ad.');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>AdMob Integration Example</Text>
        
        <Text style={styles.subtitle}>Banner Ad:</Text>
        <BannerAdComponent 
          size={BannerAdSize.BANNER}
          style={styles.bannerAd}
        />

        <Text style={styles.subtitle}>Interstitial Ad:</Text>
        <Button
          mode="contained"
          onPress={handleShowInterstitial}
          disabled={!interstitialReady}
          style={styles.button}
          buttonColor="#2196F3"
        >
          Show Interstitial Ad
        </Button>

        <Text style={styles.subtitle}>Rewarded Ad:</Text>
        <Button
          mode="contained"
          onPress={handleShowRewarded}
          disabled={!rewardedReady}
          style={styles.button}
          buttonColor="#4CAF50"
        >
          Show Rewarded Ad
        </Button>

        <Text style={styles.statusText}>
          AdMob Status: {interstitialReady && rewardedReady ? 'Ready' : 'Initializing...'}
        </Text>

        <Text style={styles.infoText}>
          Note: During development, you'll see test ads. In production, you'll need to replace the test ad unit IDs with your actual ad unit IDs from the AdMob console.
        </Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 30,
    textAlign: 'center',
    color: '#333',
  },
  subtitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 20,
    marginBottom: 10,
    color: '#555',
  },
  bannerAd: {
    marginVertical: 10,
    borderRadius: 8,
    overflow: 'hidden',
  },
  button: {
    marginVertical: 10,
    width: '100%',
  },
  statusText: {
    fontSize: 16,
    marginTop: 20,
    fontWeight: '500',
    color: '#666',
  },
  infoText: {
    fontSize: 14,
    marginTop: 20,
    textAlign: 'center',
    color: '#888',
    lineHeight: 20,
  },
});

export default AdExampleScreen;
