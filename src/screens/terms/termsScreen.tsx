import React, {useState, useEffect} from 'react';
import {
  StyleSheet,
  SafeAreaView,
  View,
  Text,
  ActivityIndicator,
} from 'react-native';
import {WebView} from 'react-native-webview';
import {COLORS} from '../../common/constant';
import axios from 'axios';
import {showErrorToast} from '../../utils/showToast';
import {useNetworkState} from '../../utils/networkStateManager';

const TermsScreen = () => {
  const [htmlContent, setHtmlContent] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const {isConnected} = useNetworkState();

  useEffect(() => {
    fetchTermsAndConditions();
  }, []);

  const fetchTermsAndConditions = async () => {
    if (!isConnected) {
      setError('No internet connection. Please check your network settings.');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await axios.get(
        'https://india-customer-care-api.apps.openxcell.dev/app/v1/static/terms-and-conditions',
        {
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );

      console.log('API Response:', JSON.stringify(response.data, null, 2));

      if (
        response.data &&
        response.data.success &&
        response.data.data &&
        response.data.data.content
      ) {
        setHtmlContent(response.data.data.content);
      } else {
        setError('No content available');
      }
    } catch (err: any) {
      console.error('Error fetching terms and conditions:', err);
      setError(err.message || 'Failed to load content');
      showErrorToast(err);
    } finally {
      setLoading(false);
    }
  };

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.centerContainer}>
          <ActivityIndicator size="large" color="#0000ff" />
          <Text style={styles.loadingText}>Loading content...</Text>
        </View>
      );
    }

    if (error) {
      return (
        <View style={styles.centerContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <Text style={styles.retryText} onPress={fetchTermsAndConditions}>
            Tap to retry
          </Text>
        </View>
      );
    }

    // Create a properly formatted HTML document with styling
    const formattedHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
              padding: 15px;
              line-height: 1.5;
              color: #333;
            }
            p {
              margin-bottom: 16px;
            }
            strong {
              font-weight: bold;
            }
            a {
              color: #0066cc;
              text-decoration: none;
            }
          </style>
        </head>
        <body>
          ${htmlContent || '<h1>No content available</h1>'}
        </body>
      </html>
    `;

    return (
      <WebView
        source={{html: formattedHtml}}
        style={styles.webview}
        originWhitelist={['*']}
        scalesPageToFit={false}
      />
    );
  };

  return (
    <SafeAreaView style={styles.container}>{renderContent()}</SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.WHITE,
  },
  webview: {
    flex: 1,
    padding: 15,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  errorText: {
    color: 'red',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 10,
  },
  retryText: {
    color: 'blue',
    fontSize: 16,
    textDecorationLine: 'underline',
  },
});

export default TermsScreen;
