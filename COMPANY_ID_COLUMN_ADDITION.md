# Company ID Column Addition

## Overview

Added a new `companyId` column to the companies table to store the original API companyId alongside the existing encrypted WatermelonDB id. This enables better linking with categories and future relationship management.

## Changes Made

### 1. Database Schema Updates

#### Schema Version Update
- Updated schema version from 4 to 5
- Added `company_id` column to companies table

#### Migration Added
```typescript
// Migration from version 4 to 5: Add company_id column to companies table
{
  toVersion: 5,
  steps: [
    addColumns({
      table: 'companies',
      columns: [
        { name: 'company_id', type: 'number' }, // Store original API companyId
      ],
    }),
  ],
}
```

### 2. Model Updates

#### Company Model (`Company.ts`)
```typescript
export default class Company extends Model {
  static table = 'companies';

  @field('company_id') companyId!: number; // NEW: Store original API companyId
  @field('company_name') companyName!: string;
  // ... other existing fields
}
```

### 3. Repository Updates

#### CompanyData Interface
```typescript
export interface CompanyData {
  id?: string; // WatermelonDB ID (string)
  company_id?: number; // NEW: Original API companyId (number)
  company_name: string;
  // ... other existing fields
}
```

#### New Repository Methods
- **`getByCompanyId(companyId: number)`**: Find company by API companyId
- Updated all CRUD methods to handle the new `company_id` field
- Updated batch operations to include `company_id`

### 4. Background Sync Updates

#### Company Sync Logic
```typescript
// Updated mapping in BackgroundSyncManager
const companyDataList: CompanyData[] = allCompanies.map(apiCompany => ({
  company_id: apiCompany.companyId || 0, // NEW: Store original API companyId
  company_name: apiCompany.companyName || '',
  // ... other fields
}));
```

### 5. Application Updates

#### Company Details Screen
```typescript
// Updated company creation for history
const companyForDb = {
  company_id: result.data.companyId, // NEW: Store original API companyId
  company_name: result.data.companyName,
  // ... other fields
};
```

#### History Repository
- Updated to include `company_id` when retrieving company details

## Database Structure

### Before (Version 4)
```sql
companies:
- id (string, WatermelonDB primary key)
- company_name (string)
- parent_company (string, optional)
- company_email (string, optional)
- company_logo_url (string, optional)
- company_country (string, optional)
- company_address (string, optional)
- company_website (string, optional)
- upvote_count (number)
- downvote_count (number)
- created_at (number)
- updated_at (number)
```

### After (Version 5)
```sql
companies:
- id (string, WatermelonDB primary key)
- company_id (number) ← NEW: Original API companyId
- company_name (string)
- parent_company (string, optional)
- company_email (string, optional)
- company_logo_url (string, optional)
- company_country (string, optional)
- company_address (string, optional)
- company_website (string, optional)
- upvote_count (number)
- downvote_count (number)
- created_at (number)
- updated_at (number)
```

## API Response Mapping

### API Response Structure
```json
{
  "companyId": 13138,
  "companyName": "My paging nation",
  "parentCompany": null,
  "companyEmail": null,
  "companyLogoUrl": null,
  "companyCountry": null,
  "companyAddress": null,
  "companyWebsite": "https://stpi.in",
  "upVoteCount": 0,
  "downVoteCount": 0,
  "createdAt": "Wed, 07 May 2025 06:27:48 GMT",
  "categories": [...]
}
```

### Database Mapping
```typescript
{
  company_id: apiCompany.companyId,        // 13138
  company_name: apiCompany.companyName,    // "My paging nation"
  company_website: apiCompany.companyWebsite, // "https://stpi.in"
  // ... other fields
}
```

## Benefits

### 1. **Relationship Management**
- Enables proper linking between companies and categories
- Facilitates joins and queries based on API relationships
- Maintains consistency with API data structure

### 2. **Data Integrity**
- Preserves original API identifiers
- Enables data validation and synchronization
- Supports future API integrations

### 3. **Query Capabilities**
```typescript
// Find company by API ID
const company = await companyRepository.getByCompanyId(13138);

// Link with company-categories
const companyCategories = await companyCategoryRepository.getByCompanyId(13138);
```

### 4. **Future-Proofing**
- Supports advanced relationship queries
- Enables efficient data synchronization
- Facilitates API-based operations

## Usage Examples

### Get Company by API ID
```typescript
const company = await watermelonCompanyRepository.getByCompanyId(13138);
if (company) {
  console.log(`Found: ${company.company_name}`);
}
```

### Create Company with API ID
```typescript
const companyData = {
  company_id: 13138,
  company_name: "My paging nation",
  company_website: "https://stpi.in"
};
const id = await watermelonCompanyRepository.create(companyData);
```

### Link with Categories
```typescript
// Get company categories using API companyId
const companyCategories = await companyCategoryRepository.getByCompanyId(13138);
console.log(`Company has ${companyCategories.length} categories`);
```

## Testing

### Verify Migration
```typescript
// Test that companyId field is properly stored
await WeeklySyncTester.forceSyncForTesting();
await WeeklySyncTester.getSyncInfo(); // Shows sample company with companyId
```

### Verify Relationships
```typescript
// Test company-category relationships
const company = await companyRepository.getByCompanyId(13138);
const categories = await companyCategoryRepository.getByCompanyId(13138);
console.log(`Company ${company?.company_name} has ${categories.length} categories`);
```

## Migration Notes

### Automatic Migration
- Existing installations will automatically migrate to version 5
- New `company_id` column will be added with default value 0
- Next sync will populate the column with correct API values

### Data Consistency
- After migration, force a company sync to populate `company_id` values
- Use `backgroundSyncManager.forceSyncCompanies()` for immediate update
- Verify data integrity with test utilities

## Backward Compatibility

### Existing Code
- All existing code continues to work unchanged
- WatermelonDB `id` field remains the primary key
- New `company_id` field is optional in interfaces

### API Compatibility
- Maintains full compatibility with existing API responses
- No changes required to API endpoints
- Preserves all existing functionality

## Future Enhancements

### Relationship Queries
```typescript
// Future: Advanced relationship queries
const companiesInCategory = await companyRepository.getByCategoryId(1);
const categoryCompanies = await companyCategoryRepository.getCompaniesWithCategories();
```

### Data Synchronization
```typescript
// Future: Incremental sync based on API IDs
const updatedCompanies = await companyApiService.getUpdatedSince(lastSyncDate);
const localCompanies = await companyRepository.getByCompanyIds(updatedCompanies.map(c => c.companyId));
```

This enhancement provides a solid foundation for future relationship management and data synchronization improvements.
