# lastDateTime Save Behavior Implementation

## Overview

Updated the sync logic to save `lastDataTime` immediately after the first successful API response, ensuring that subsequent app launches always have a timestamp for incremental sync, even if the sync process is interrupted.

## 🔄 Updated Behavior

### **First App Launch**
1. **Initial State**: `lastDataTime = 0` (no previous timestamp)
2. **API Call**: `/company/get-all?page=1&limit=500&sortBy=created_at&sortOrder=DESC` (no lastDateTime parameter)
3. **After First Successful Response**: Save current timestamp as `lastDataTime`
4. **Continue Processing**: Process remaining pages and save data to database
5. **Final State**: `lastDataTime = <current_timestamp>` saved for next launch

### **Subsequent App Launches**
1. **Initial State**: `lastDataTime = <saved_timestamp>` (from previous launch)
2. **API Call**: `/company/get-all?page=1&limit=500&sortBy=created_at&sortOrder=DESC&lastDateTime=<saved_timestamp>`
3. **After First Successful Response**: Update `lastDataTime` with new current timestamp
4. **Continue Processing**: Process remaining pages and save data to database
5. **Final State**: `lastDataTime = <new_timestamp>` saved for next launch

## 🔧 Implementation Details

### Key Changes Made

#### 1. **Immediate Timestamp Saving**
```typescript
// Save the current timestamp as lastDataTime immediately after successful API call
// This ensures we have a timestamp for next launch even if sync process is interrupted
const currentTimestamp = Math.floor(Date.now() / 1000); // Convert to seconds

if (syncState) {
  await syncStateRepository.createOrUpdate({
    ...syncState,
    lastDataTime: currentTimestamp,
  });
  console.log(`[BackgroundSync] ✅ Saved lastDataTime: ${currentTimestamp} (${new Date(currentTimestamp * 1000).toISOString()}) for next app launch`);
}
```

#### 2. **Timing of Save Operation**
- **Before**: `lastDataTime` was only saved at the very end of the sync process
- **After**: `lastDataTime` is saved immediately after the first successful API response

#### 3. **Handling Empty Responses**
```typescript
// If no companies returned, mark as completed
if (allCompanies.length === 0) {
  console.log('[BackgroundSync] No new/updated companies found, sync completed');
  
  if (syncState) {
    await syncStateRepository.createOrUpdate({
      ...syncState,
      status: 'completed',
      progress: 100,
      lastSyncAt: new Date(),
      lastDataTime: currentTimestamp, // Ensure lastDataTime is saved even for empty responses
    });
  }
  return;
}
```

## 📱 User Experience Flow

### Scenario 1: Fresh Install
```
App Launch #1:
├── lastDataTime: 0
├── API Call: /company/get-all?page=1&limit=500&sortBy=created_at&sortOrder=DESC
├── Response: 500 companies received
├── Save: lastDataTime = 1749789605 (immediately)
├── Process: Continue with remaining pages and database operations
└── Complete: Ready for next launch

App Launch #2 (after kill/restart):
├── lastDataTime: 1749789605 (from previous launch)
├── API Call: /company/get-all?page=1&limit=500&sortBy=created_at&sortOrder=DESC&lastDateTime=1749789605
├── Response: Only new/updated companies since timestamp
├── Save: lastDataTime = 1749789650 (immediately)
└── Process: Continue with incremental updates
```

### Scenario 2: Interrupted Sync
```
App Launch #1:
├── lastDataTime: 0
├── API Call: /company/get-all?page=1&limit=500&sortBy=created_at&sortOrder=DESC
├── Response: 500 companies received
├── Save: lastDataTime = 1749789605 (immediately)
├── Process: App crashes/killed during database operations
└── State: lastDataTime = 1749789605 is still saved

App Launch #2 (after crash):
├── lastDataTime: 1749789605 (preserved from previous launch)
├── API Call: /company/get-all?page=1&limit=500&sortBy=created_at&sortOrder=DESC&lastDateTime=1749789605
└── Result: Only fetches data newer than the saved timestamp
```

## 🎯 Benefits

1. **Resilient to Interruptions**: Timestamp is saved immediately, not at the end
2. **Consistent Incremental Sync**: Always has a reference point for next launch
3. **Reduced Data Transfer**: Subsequent launches only fetch new/updated data
4. **Better Performance**: Faster sync times after first launch
5. **Crash Recovery**: Graceful handling of app crashes during sync

## 🧪 Testing Scenarios

### Test 1: Normal Flow
1. Fresh install → First launch → Verify `lastDataTime` saved after API response
2. Kill app → Relaunch → Verify API called with saved `lastDateTime` parameter

### Test 2: Interrupted Sync
1. Fresh install → First launch → Kill app during database operations
2. Relaunch → Verify API called with saved `lastDateTime` parameter

### Test 3: Empty Response
1. Launch app with existing `lastDataTime`
2. API returns empty array (no new data)
3. Verify `lastDataTime` is still updated with current timestamp

## 📝 Logging Examples

### First Launch
```
[BackgroundSync] Using lastDateTime: 0 (first launch - parameter not included)
[CompanyAPI] Fetching companies page 1 with lastDateTime: not included
[BackgroundSync] ✅ Saved lastDataTime: 1749789605 (2025-06-13T10:20:05.000Z) for next app launch
```

### Subsequent Launch
```
[BackgroundSync] Using lastDateTime: 1749789605 (2025-06-13T10:20:05.000Z - parameter included)
[CompanyAPI] Fetching companies page 1 with lastDateTime: 1749789605
[BackgroundSync] ✅ Saved lastDataTime: 1749789650 (2025-06-13T10:20:50.000Z) for next app launch
```

This implementation ensures that the app always has a proper timestamp for incremental sync, regardless of when the sync process might be interrupted.
