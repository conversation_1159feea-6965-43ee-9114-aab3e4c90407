import React from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import WeeklySyncTester from '../utils/testWeeklySync';

const DatabaseFixComponent = () => {
  
  const fixDatabaseSchema = async () => {
    try {
      Alert.alert(
        'Fix Database Schema',
        'This will reset the database and re-sync all data. Continue?',
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Fix Now', 
            onPress: async () => {
              console.log('🔧 Starting database schema fix...');
              
              // Reset the database to fix schema mismatch
              await WeeklySyncTester.resetDatabaseForSchemaFix();
              
              Alert.alert(
                'Database Reset Complete',
                'Please restart the app and wait for sync to complete.',
                [{ text: 'OK' }]
              );
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error fixing database:', error);
      Alert.alert('Error', 'Failed to fix database. Try manual reset.');
    }
  };

  const checkDatabaseIntegrity = async () => {
    try {
      console.log('🔍 Checking database integrity...');
      await WeeklySyncTester.checkDatabaseIntegrity();
      Alert.alert('Check Complete', 'See console for results');
    } catch (error) {
      console.error('Error checking database:', error);
      Alert.alert('Error', 'Database integrity check failed');
    }
  };

  return (
    <View style={{ padding: 20, backgroundColor: '#f0f0f0', margin: 20 }}>
      <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 10 }}>
        Database Schema Fix
      </Text>
      
      <TouchableOpacity
        onPress={checkDatabaseIntegrity}
        style={{ 
          backgroundColor: '#007AFF', 
          padding: 15, 
          borderRadius: 8, 
          marginBottom: 10 
        }}
      >
        <Text style={{ color: 'white', textAlign: 'center' }}>
          Check Database Integrity
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        onPress={fixDatabaseSchema}
        style={{ 
          backgroundColor: '#FF3B30', 
          padding: 15, 
          borderRadius: 8 
        }}
      >
        <Text style={{ color: 'white', textAlign: 'center' }}>
          Fix Database Schema Error
        </Text>
      </TouchableOpacity>
      
      <Text style={{ fontSize: 12, color: '#666', marginTop: 10 }}>
        Use "Check" first to diagnose, then "Fix" to resolve the error.
      </Text>
    </View>
  );
};

export default DatabaseFixComponent;
