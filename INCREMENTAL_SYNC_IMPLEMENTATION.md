# Incremental Sync Implementation with lastDateTime Parameter

## Overview

This implementation updates the background sync behavior to call company APIs on every app launch using the `lastDateTime` parameter for incremental updates, while keeping the company-categories API on a weekly schedule.

## 🔄 Updated Behavior

### **Company APIs** (`/company/get-all` and `/company`)

- **Previous**: Called once a week
- **New**: Called on every app launch with `lastDateTime` parameter
- **First Launch**: `lastDateTime=0` (gets all data)
- **Subsequent Launches**: `lastDateTime=<saved_timestamp>` (gets only new/updated data)

### **Company Categories API** (`/data/company-categories`)

- **Unchanged**: Still called once a week
- No changes to request parameters

### **Categories API** (`/category`)

- **Unchanged**: Still called once a week

## 📋 Files Modified

### 1. Database Schema Changes

#### `src/database/watermelon/schema.ts`

- **Version**: Updated from 2 to 3
- **Added**: `last_data_time` field to `sync_state` table (number, optional)
- **Purpose**: Store epoch timestamp for `lastDateTime` API parameter

#### `src/database/watermelon/models/SyncState.ts`

- **Added**: `@field('last_data_time') lastDataTime?: number`

#### `src/database/watermelon/repositories/syncStateRepository.ts`

- **Added**: `lastDataTime?: number` to `SyncStateData` interface
- **Updated**: All CRUD operations to handle `lastDataTime` field

#### `src/database/watermelon/migrations.ts`

- **Added**: Migration from version 2 to 3
- **Action**: `addColumns` to add `last_data_time` field to existing `sync_state` table

### 2. API Service Updates

#### `src/services/companyApiService.ts`

- **Updated**: `fetchCompaniesPage()` method to accept optional `lastDateTime` parameter
- **Updated**: `fetchCompaniesByCategory()` method to accept optional `lastDateTime` parameter
- **Updated**: `fetchAllCompanies()` method to accept optional `lastDateTime` parameter
- **Added**: Logging for `lastDateTime` parameter usage

### 3. Repository Updates

#### `src/database/watermelon/repositories/companyRepository.ts`

- **Added**: `createOrUpdateByCompanyId()` method for API-based upserts
- **Added**: `batchCreateOrUpdate()` method for efficient incremental updates
- **Purpose**: Enable upsert operations based on API `company_id` instead of WatermelonDB ID

#### `src/database/watermelon/repositories/numberRepository.ts`

- **Added**: `clearByCompanyId()` method to clear numbers for specific companies
- **Purpose**: Support incremental number updates

### 4. Background Sync Manager Updates

#### `src/services/backgroundSyncManager.ts`

- **Removed**: Weekly check logic for companies (`shouldSyncCompanies()` calls)
- **Updated**: `syncCompanies()` method with complete incremental logic:
  - Uses `lastDataTime` from sync state (0 for first launch)
  - Calls API with `lastDateTime` parameter
  - Handles empty responses gracefully
  - Uses `batchCreateOrUpdate()` instead of clear + create
  - Saves current timestamp as new `lastDataTime`
- **Updated**: `forceSyncCompanies()` to reset `lastDataTime=0` for full sync

## 🔧 Technical Implementation Details

### Incremental Sync Logic

1. **Get Last Data Time**: Retrieve `lastDataTime` from sync state (0 if none)
2. **API Call**: Call `/company/get-all` with conditional `lastDateTime` parameter
3. **Handle Response**:
   - If empty: Mark sync as completed, no database changes
   - If data: Process for incremental update
4. **Database Update**: Use `batchCreateOrUpdate()` for companies
5. **Numbers Update**: Clear existing numbers for updated companies, add new ones
6. **Save Timestamp**: Store current timestamp as `lastDataTime` for next sync

### lastDateTime Parameter Handling

The `lastDateTime` parameter is conditionally included in API requests:

- **Include Parameter**: When `lastDateTime > 0` (valid timestamp)
- **Exclude Parameter**: When `lastDateTime` is `0`, `null`, or `undefined`

**Logic Implementation:**

```typescript
const lastDateTimeParam =
  lastDateTime && lastDateTime > 0 ? `&lastDateTime=${lastDateTime}` : '';
```

**Benefits:**

- Cleaner API requests for first-time syncs
- Server can differentiate between full sync requests and incremental sync requests
- Avoids potential server-side issues with `lastDateTime=0` parameter

### Data Flow

```
App Launch → Get lastDataTime → API Call with lastDateTime →
Process Response → Incremental DB Update → Save New Timestamp
```

### API Request Examples

**First Launch (lastDateTime = 0):**

```
/company/get-all?page=1&limit=500&sortBy=created_at&sortOrder=DESC
```

_Note: lastDateTime parameter is not included when value is 0, null, or undefined_

**Subsequent Launches (lastDateTime = 1749723478):**

```
/company/get-all?page=1&limit=500&sortBy=created_at&sortOrder=DESC&lastDateTime=1749723478
```

_Note: lastDateTime parameter is only included when it has a valid value > 0_

## 🎯 Benefits

1. **Reduced Data Transfer**: Only new/updated records are fetched
2. **Faster Sync**: Smaller payloads mean quicker sync times
3. **Better Performance**: Incremental updates instead of full rebuilds
4. **Always Fresh**: Data is updated on every app launch
5. **Bandwidth Efficient**: Minimal data usage for regular users

## 🔄 Migration Strategy

1. **Database Migration**: Automatic migration from version 2 to 3 adds `last_data_time` field
2. **First Launch After Update**: `lastDataTime` will be 0, triggering full sync
3. **Subsequent Launches**: Will use incremental sync with saved timestamp
4. **Backward Compatibility**: Existing sync logic for categories and company-categories unchanged

## 🧪 Testing Recommendations

1. **Fresh Install**: Test with `lastDateTime=0` (should get all data)
2. **Incremental Update**: Test with existing timestamp (should get only new data)
3. **Empty Response**: Test when no new data available
4. **Force Sync**: Test `forceSyncCompanies()` resets to full sync
5. **Error Handling**: Test API failures and retry logic

## 📝 Notes

- Company categories API (`/data/company-categories`) maintains weekly sync schedule
- Categories API (`/category`) maintains weekly sync schedule
- Numbers are handled with clear + recreate strategy for updated companies
- Force sync resets `lastDataTime=0` to trigger full data fetch
- All logging includes `lastDateTime` values for debugging
