# Number Object Structure Implementation

## Overview

This implementation updates the local database schema and sync logic to handle the new number object structure from the company/get-all API response, while maintaining backward compatibility with existing functionality.

## Changes Made

### 1. Database Schema Updates

#### New Numbers Table

- **Table**: `numbers`
- **Purpose**: Store detailed number data from the new API response structure
- **Columns**:
  - `api_number_id` (number): Original API numberId
  - `company_id` (number): API companyId (foreign key)
  - `number` (string): Phone number
  - `description` (string): Number description
  - `type` (string): TOLL_FREE, ALL_INDIA, or INTERNATIONAL
  - `upvote_count` (number): Vote count
  - `downvote_count` (number): Vote count
  - `is_whatsapp` (number): 0 or 1 (boolean as number)
  - `created_at` (number): Timestamp
  - `updated_at` (number): Timestamp

#### Schema Version

- Updated from version 1 to version 2
- Added migration to create the new numbers table

### 2. New Models and Repositories

#### Number Model (`Number.ts`)

- WatermelonDB model for the numbers table
- Includes all necessary field decorators

#### Number Repository (`numberRepository.ts`)

- CRUD operations for numbers
- `getByCompanyIdGrouped()`: Returns numbers grouped by type (TOLL_FREE, ALL_INDIA, INTERNATIONAL)
- Batch operations for efficient data handling

#### Number Utilities (`numberUtils.ts`)

- `extractFirstAvailableNumber()`: Extracts first available number with priority (TOLL_FREE → ALL_INDIA → INTERNATIONAL)
- `extractFirstAvailableLocalNumber()`: Same logic for local database numbers
- `getFirstAvailableNumberType()`: Returns the type of the first available number
- Handles both old string format and new object format

### 3. API Interface Updates

#### Updated ApiCompany Interface

```typescript
export interface ApiCompany {
  // ... existing fields
  number?:
    | string
    | {
        TOLL_FREE?: ApiNumber[];
        ALL_INDIA?: ApiNumber[];
        INTERNATIONAL?: ApiNumber[];
      }
    | null; // Supports both old string format and new object format
}
```

#### New ApiNumber Interface

```typescript
export interface ApiNumber {
  numberId: number;
  companyId: number;
  number: string;
  description: string;
  type: string;
  upvoteCount: number;
  downvoteCount: number;
  isWhatsapp: boolean;
}
```

### 4. Background Sync Updates

#### Enhanced Company Sync Logic

- **Backward Compatibility**: Handles both string and object formats for the `number` field
- **Number Extraction**: Extracts numbers from the new object structure and saves them to the numbers table
- **First Available Number**: Uses utility function to extract first available number with priority (TOLL_FREE → ALL_INDIA → INTERNATIONAL)
- **Card UI Support**: Stores first available number in companies table for backward compatibility with card UI
- **Progress Tracking**: Updated progress tracking to include number saving (90%-100%)
- **Cleanup**: Clears both companies and numbers tables before sync

#### Sync Process

1. Clear existing companies and numbers (10% progress)
2. Fetch companies with pagination (10%-80% progress)
3. Convert companies and extract numbers (80%-90% progress)
   - Extract first available number for card UI display
   - Extract all numbers for detailed storage
4. Save companies with first available number (80%-90% progress)
5. Save all numbers if available (90%-100% progress)

### 5. CompanyDetailsScreen Updates

#### Smart Data Loading

- **Local First**: Tries to load numbers from local database first
- **Fallback Logic**: Only calls API if no local numbers are found
- **Efficient**: Avoids unnecessary API calls when data is already available locally
- **Offline Support**: Uses local data when available, regardless of connectivity

#### Enhanced Functions

- `loadLocalNumbers()`: Loads numbers from local database and converts to UI format
- `getCompanyDetails()`: Updated to use local-first approach
- Maintains existing UI behavior and search functionality

### 6. Card UI Updates

#### CustomCategoriesDetailCard

- **No Changes Required**: Card UI continues to work with existing `number` field
- **Automatic Priority**: Displays first available number based on priority (TOLL_FREE → ALL_INDIA → INTERNATIONAL)
- **Backward Compatible**: Works with both old string format and new prioritized number

#### Data Flow for Card UI

1. **Sync Process**: Extracts first available number and stores in `companies.number` field
2. **Categories Screen**: Loads companies from local DB, passes `number` field to card
3. **History Screen**: Loads companies from local DB, passes `number` field to card
4. **Card Component**: Displays the prioritized number without any changes needed

## Backward Compatibility

### API Response Handling

- **String Format**: Still supported for existing API responses
- **Object Format**: New format is processed and stored in numbers table
- **Graceful Degradation**: Falls back to API call if local data is unavailable

### Database Migration

- **Automatic**: Existing installations migrate from version 1 to 2
- **Non-destructive**: Existing data is preserved
- **Safe**: Migration only adds new table, doesn't modify existing tables

### UI Compatibility

- **Same Interface**: ContactNumber interface remains unchanged
- **Same Behavior**: UI behavior and search functionality unchanged
- **Seamless**: Users won't notice the difference in functionality

## Benefits

### Performance

- **Reduced API Calls**: Uses local data when available
- **Offline Support**: Works without internet connection
- **Faster Loading**: Local database queries are faster than API calls

### Data Management

- **Structured Storage**: Numbers are properly normalized in separate table
- **Better Queries**: Can query numbers independently of companies
- **Scalability**: Supports large datasets efficiently

### Future-Proofing

- **Extensible**: Easy to add new number types or fields
- **Maintainable**: Clear separation of concerns
- **Flexible**: Supports both old and new API formats

## Testing Recommendations

### Database Migration

1. Test migration from version 1 to 2
2. Verify new numbers table is created correctly
3. Ensure existing data is preserved

### Sync Functionality

1. Test with old API format (string number field)
2. Test with new API format (object number field)
3. Verify numbers are extracted and saved correctly
4. Test progress tracking during sync

### CompanyDetailsScreen

1. Test loading with local numbers available
2. Test loading without local numbers (API fallback)
3. Test offline behavior
4. Test search functionality with local numbers
5. Verify UI behavior remains consistent

### Edge Cases

1. Empty number arrays in API response
2. Missing number field in API response
3. Network connectivity changes during loading
4. Large datasets with many numbers per company

## Migration Path

### For Existing Users

1. App update installs new schema version 2
2. Migration automatically creates numbers table
3. Next company sync populates numbers from API
4. CompanyDetailsScreen starts using local numbers

### For New Users

1. Fresh installation uses schema version 2
2. Initial sync populates both companies and numbers
3. All functionality works with local-first approach

## Monitoring

### Logging

- Sync progress and number extraction
- Local vs API data usage
- Performance metrics for database operations
- Error handling for migration and sync

### Metrics to Track

- Number of companies with local numbers
- API call reduction percentage
- Offline usage patterns
- Sync success rates
