diff --git a/node_modules/@react-native/gradle-plugin/.gradle/buildOutputCleanup/buildOutputCleanup.lock b/node_modules/@react-native/gradle-plugin/.gradle/buildOutputCleanup/buildOutputCleanup.lock
new file mode 100644
index 0000000..58a1b7c
Binary files /dev/null and b/node_modules/@react-native/gradle-plugin/.gradle/buildOutputCleanup/buildOutputCleanup.lock differ
diff --git a/node_modules/@react-native/gradle-plugin/.gradle/buildOutputCleanup/cache.properties b/node_modules/@react-native/gradle-plugin/.gradle/buildOutputCleanup/cache.properties
new file mode 100644
index 0000000..2d2fc92
--- /dev/null
+++ b/node_modules/@react-native/gradle-plugin/.gradle/buildOutputCleanup/cache.properties
@@ -0,0 +1,2 @@
+#Wed May 07 14:49:13 IST 2025
+gradle.version=8.0.2
diff --git a/node_modules/@react-native/gradle-plugin/.gradle/buildOutputCleanup/outputFiles.bin b/node_modules/@react-native/gradle-plugin/.gradle/buildOutputCleanup/outputFiles.bin
new file mode 100644
index 0000000..e566079
Binary files /dev/null and b/node_modules/@react-native/gradle-plugin/.gradle/buildOutputCleanup/outputFiles.bin differ
diff --git a/node_modules/@react-native/gradle-plugin/shared/build.gradle.kts b/node_modules/@react-native/gradle-plugin/shared/build.gradle.kts
index b705a7c..9818aed 100644
--- a/node_modules/@react-native/gradle-plugin/shared/build.gradle.kts
+++ b/node_modules/@react-native/gradle-plugin/shared/build.gradle.kts
@@ -33,8 +33,8 @@ tasks.withType<KotlinCompile>().configureEach {
     apiVersion.set(KotlinVersion.KOTLIN_1_7)
     // See comment above on JDK 11 support
     jvmTarget.set(JvmTarget.JVM_11)
-    allWarningsAsErrors =
-        project.properties["enableWarningsAsErrors"]?.toString()?.toBoolean() ?: false
+    allWarningsAsErrors.set(
+    project.properties["enableWarningsAsErrors"]?.toString()?.toBoolean() ?: false)
   }
 }
 
